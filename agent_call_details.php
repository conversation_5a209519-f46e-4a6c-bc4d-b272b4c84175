<!DOCTYPE html>
<html>
<head>
<?php
        $page_name = "Agent Call Details";
        include_once("inc/head.php");
        $id = $_REQUEST['id'];
        if(!isset($_SESSION['tp_app']) && $_SESSION['tp_app'] == ""){
            header("location:index.php");
            exit();
        }else if($id == ""){
            header("location:index.php");
            exit();
        }
        
        $selectuser = mysqli_query($link,"select tp_u_mobile from tp_salesman where tp_u_id='".$id."'");
        $rwselectuser = mysqli_fetch_array($selectuser);
        
        $selectpackunit = mysqli_query($link,"select * from tp_calls where tc_cd_agent_number='".$rwselectuser['tp_u_mobile']."' ORDER BY tc_call_id DESC");
        $nmselectpackunit = mysqli_num_rows($selectpackunit);
?>
</head>
<body>
      <?php include_once("inc/top-pan.php"); ?>
      <main id="main" class="main">
          <div class="pagetitle">
              <h1>Agent Call Details</h1>
              <nav>
                  <ol class="breadcrumb">
                      <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>dashboard.php">Dashboard</a></li>
                      <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>users.php.php">Agent Lists</a></li>
                      <li class="breadcrumb-item">Agent Call Details</li>
                  </ol>
              </nav>
          </div>
          <section class="section">
              <?php if(isset($_SESSION["msg"])){echo '<div class="container-fluid">'.$_SESSION["msg"].'</div>';}unset($_SESSION["msg"]); ?>
              <div class="row"><div class="col-lg-12"><div class="float-end mb-3"><a class="btn btn-primary" href="users.php">Back</a></div></div></div>
              <div class="row">
                  <div class="col-lg-12">
                      <div class="card">
                          <div class="card-body">
                              <h5 class="card-title">Agent Call Details</h5>
                              <table class="table datatable">
                                  <thead>
                                      <tr>
                                          <th>ID</th>
                                          <th>Agent Number</th>
                                          <th>Customer Number</th>
                                          <th>Direction</th>
                                          <th>Disconnected By</th>
                                          <th>Start Time</th>
                                          <th>End Time</th>
                                          <th>Action</th>
                                      </tr>
                                  </thead>
                                  <tbody>
                                      <?php if($nmselectpackunit > 0){ 
                                              while($rwselectpackunit = mysqli_fetch_array($selectpackunit)){
                                      ?>
                                                <tr>
                                                  <td><a href="javascript:void(0);" class="popup_cls" data-id="<?php echo $rwselectpackunit['tc_call_id']; ?>"><?php echo $rwselectpackunit['tc_call_id']; ?></a></td>  
                                                  <td><?php echo $rwselectpackunit['tc_cd_agent_number']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tc_cd_customer_number']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tc_cd_call_direction']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tc_cd_disconnected_by']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tc_cd_start_date_time']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tc_cd_end_date_time']; ?></td>
                                                  <td>
                                                      <?php if($rwselectpackunit['tc_rd_recording_path'] != ""){ ?>
                                                            <a class="btn btn-sm btn-primary open_call_popup" data-id="<?php echo $rwselectpackunit['tc_call_id']; ?>"><i class="bi bi-headset"></i></a>
                                                      <?php } ?>
                                                  </td>
                                                </tr>
                                      <?php    }
                                            }else{ ?>
                                              <tr>
                                                <td class="text-danger text-center" colspan="8">No Call Details Found.</td>
                                              </tr>
                                      <?php } ?>
                                  </tbody>
                              </table>
                          </div>
                      </div>
                  </div>
              </div>
          </section>
      </main>
      <div class="modal fade" id="call_details" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"></div>
      <?php include_once("inc/footer.php"); ?>
      <?php include_once("js.php"); ?>
      <script>
          jQuery(document).ready(function(){
              jQuery(document).on('click','.popup_cls',function(){
                  var dataid = jQuery(this).data("id");
                  jQuery.ajax({
                    type:'post',
                    url:base_url+'users_db.php',
                    data:"action=popupdata&id="+dataid,
                    success:function(url){
                      jQuery("#call_details").html(url);
                      jQuery("#call_details").modal('show');
                    },
                  });
              });
              jQuery(document).on('click','.open_call_popup',function(){
                  var dataid = jQuery(this).data("id");
                  jQuery.ajax({
                    type:'post',
                    url:base_url+'users_db.php',
                    data:"action=callpopupdata&id="+dataid,
                    success:function(url){
                      jQuery("#call_details").html(url);
                      jQuery("#call_details").modal('show');
                    },
                  });
              });
          });
      </script>
</body>
</html>