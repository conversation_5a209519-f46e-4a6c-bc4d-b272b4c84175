<!DOCTYPE html>
<html>
<head>
<?php
        if (isset ($_REQUEST['id']) ) {  
            $page_name = "Edit Patient";
        }else{
            $page_name = "Add Patient";
        }
        include_once("inc/head.php");
        if(!isset($_SESSION['tp_app']) && $_SESSION['tp_app'] == ""){
            header("location:index.php");
            exit();
        }
        if (isset ($_REQUEST['id']) ) { 
            $selectpackunit = mysqli_query($link,"select * from tp_patients where tp_p_id='".$_REQUEST['id']."'");
            $rwselectpackunit = mysqli_fetch_array($selectpackunit);
            $action = "edit";
            $id = $_REQUEST['id'];
            $tp_p_firstname = $rwselectpackunit['tp_p_firstname'];
            $tp_p_middlename = isset($rwselectpackunit['tp_p_middlename']) ? $rwselectpackunit['tp_p_middlename'] : "";
            $tp_p_lastname = $rwselectpackunit['tp_p_lastname'];
            $tp_p_mobile_no = $rwselectpackunit['tp_p_mobile_no'];
            $tp_p_whatsapp_no = isset($rwselectpackunit['tp_p_whatsapp_no']) ? $rwselectpackunit['tp_p_whatsapp_no'] : $tp_p_mobile_no;
        }else{
            $action = "add";
            $id = "";
            $tp_p_firstname = "";
            $tp_p_middlename = "";
            $tp_p_lastname = "";
            $tp_p_mobile_no = "";
            $tp_p_whatsapp_no = "";
        }
?>
</head>
<body>
    <?php include_once("inc/top-pan.php"); ?>
    <main id="main" class="main">
        <div class="pagetitle">
            <h1><?php echo $page_name; ?></h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>patients.php">Patients</a></li>
                    <li class="breadcrumb-item"><?php if($action == "edit"){echo "Edit";}else{echo "Add";} ?> Patient</li>
                </ol>
            </nav>
        </div>
        <section class="section">
            <?php if(isset($_SESSION["msg"])){echo '<div class="container-fluid">'.$_SESSION["msg"].'</div>';}unset($_SESSION["msg"]); ?>
            <div class="row"><div class="col-lg-12"><div class="float-end mb-3"><a class="btn btn-primary" href="patients.php">Back</a></div></div></div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Patient Form</h5>
                            <form id="change_profile" name="change_profile" action="patients_db.php" method="post" autocomplete="off">
                                <div class="row mb-3">
                                    <label for="tp_p_firstname" class="col-sm-3 col-form-label">First Name <span>*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_p_firstname" name="tp_p_firstname" placeholder="Enter First Name" value="<?php echo $tp_p_firstname; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_p_middlename" class="col-sm-3 col-form-label">Middle Name</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_p_middlename" name="tp_p_middlename" placeholder="Enter Middle Name" value="<?php echo $tp_p_middlename; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_p_lastname" class="col-sm-3 col-form-label">Last Name <span>*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_p_lastname" name="tp_p_lastname" placeholder="Enter Last Name" value="<?php echo $tp_p_lastname; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_p_mobile_no" class="col-sm-3 col-form-label">Mobile No. <span>*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_p_mobile_no" name="tp_p_mobile_no" placeholder="Enter Mobile No." value="<?php echo $tp_p_mobile_no; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_p_whatsapp_no" class="col-sm-3 col-form-label">WhatsApp No. <span>*</span></label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="tp_p_whatsapp_no" name="tp_p_whatsapp_no" placeholder="Enter WhatsApp No." value="<?php echo $tp_p_whatsapp_no; ?>">
                                            <div class="input-group-text">
                                                <input type="checkbox" id="same_as_mobile" name="same_as_mobile" value="1" style="margin-top:0.2em;" <?php if($action == 'edit' && ($tp_p_whatsapp_no == $tp_p_mobile_no || $tp_p_whatsapp_no == "")){echo 'checked';} ?>>
                                                <label for="same_as_mobile" style="margin-left:5px;margin-bottom:0;">Same as Mobile No.</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <?php 
                                        $selecttype = mysqli_query($link,"select * from tp_querytype ORDER BY tp_qt_name ASC");
                                        while($rwselecttype = mysqli_fetch_array($selecttype)){
                                    ?>
                                            <div class="col-sm-3">
                                                <div class="row mb-3">
                                                    <label class="col-sm-12 col-form-label"><?php echo $rwselecttype['tp_qt_name']; ?></label>
                                                    <div class="col-sm-12">
                                                        <input type="hidden" value="<?php echo $rwselecttype['tp_qt_id']; ?>" name="tp_p_query_type_id[]"/>
                                                        <select class="form-control" id="tp_p_query_type" name="tp_p_query_type[]">
                                                    <option value="">Select User</option>
                                                    <?php 
                                                    $arraysel = array();
                                                    if($action == "edit"){
                                                        $selectassign = mysqli_query($link,"select * from tp_patients_assign where tp_p_patient_id='".$id."' and tp_p_q_type_id='".$rwselecttype['tp_qt_id']."'");
                                                        if(mysqli_num_rows($selectassign)>0){
                                                            $rwselectassign = mysqli_fetch_array($selectassign);
                                                             array_push($arraysel,$rwselectassign['tp_p_q_user_id']);
                                                        }else{
                                                             array_push($arraysel,$rwselecttype['tp_qt_default_user']);
                                                        }
                                                    }else{
                                                        array_push($arraysel,$rwselecttype['tp_qt_default_user']);
                                                    }
                                                    $selectuser2 = mysqli_query($link,"select * from tp_salesman where `tp_u_q_type` LIKE '%".$rwselecttype['tp_qt_id']."%' ORDER BY tp_u_full_name ASC"); 
                                                    while($rwselectuser2 = mysqli_fetch_array($selectuser2)){
                                                    ?>
                                                    <option value="<?php echo $rwselectuser2['tp_u_id']; ?>" <?php if(in_array($rwselectuser2['tp_u_id'],$arraysel)){echo "selected";} ?>><?php echo $rwselectuser2['tp_u_full_name']; ?></option>
                                                    <?php } ?>
                                                </select>
                                                    </div>
                                                </div>
                                            </div>
                                    <?php } ?>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-12">
                                        <input type="hidden" name="action" id="action" value="<?php echo $action; ?>">
                                        <input type="hidden" name="id" id="id" value="<?php echo $id; ?>">
                                        <button type="submit" class="btn btn-primary" id="add_bottom" disabled="disabled">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <?php include_once("inc/footer.php"); ?>
    <?php include_once("js.php"); ?>
    <script>
        jQuery('#same_as_mobile,#tp_p_firstname,#tp_p_middlename,#tp_p_lastname,#tp_p_mobile_no,#tp_p_whatsapp_no,#tp_p_query_type').on("keyup change",function(){
            jQuery("#add_bottom").removeAttr('disabled');
            jQuery("#tp_p_mobile_no").removeClass("error-border");
            jQuery("#tp_p_whatsapp_no").removeClass("error-border");
            jQuery("#tp_p_firstname").removeClass("error-border");
            jQuery("#tp_p_lastname").removeClass("error-border");
        });
        // Auto-fill WhatsApp No. from Mobile No. if checkbox is checked
        function syncWhatsappWithMobile() {
            var isChecked = jQuery('#same_as_mobile').is(':checked');
            var mobile = jQuery('#tp_p_mobile_no').val();
            if (isChecked) {
                jQuery('#tp_p_whatsapp_no').val(mobile).prop('readonly', true);
            } else {
                jQuery('#tp_p_whatsapp_no').prop('readonly', false);
            }
        }
        jQuery('#same_as_mobile').on('change', function() {
            syncWhatsappWithMobile();
        });
        jQuery('#tp_p_mobile_no').on('keyup change', function() {
            if (jQuery('#same_as_mobile').is(':checked')) {
                jQuery('#tp_p_whatsapp_no').val(jQuery(this).val());
            }
        });
        // On page load, set WhatsApp field state
        jQuery(document).ready(function() {
            syncWhatsappWithMobile();
        });
        jQuery("#add_bottom").click(function () {
            lfg = 1;
            var tp_p_firstname = jQuery.trim(jQuery("#tp_p_firstname").val());
            var tp_p_lastname = jQuery.trim(jQuery("#tp_p_lastname").val());
            var tp_p_mobile_no = jQuery.trim(jQuery("#tp_p_mobile_no").val());
            var tp_p_whatsapp_no = jQuery.trim(jQuery("#tp_p_whatsapp_no").val());
            if (tp_p_firstname == "") {
                lfg = 0;
                jQuery("#tp_p_firstname").addClass("error-border");
                jQuery("#tp_p_firstname").focus();
                return false;
            }
            if (tp_p_lastname == "") {
                lfg = 0;
                jQuery("#tp_p_lastname").addClass("error-border");
                jQuery("#tp_p_lastname").focus();
                return false;
            }
            if (tp_p_mobile_no == "") {
                lfg = 0;
                jQuery("#tp_p_mobile_no").addClass("error-border");
                jQuery("#tp_p_mobile_no").focus();
                return false;
            }
            if (tp_p_whatsapp_no == "") {
                lfg = 0;
                jQuery("#tp_p_whatsapp_no").addClass("error-border");
                jQuery("#tp_p_whatsapp_no").focus();
                return false;
            }
        });
    </script>
</body>
</html>