<?php include_once 'inc/connection.php';
      include_once 'inc/functions.php';
      $action = $_POST['action'];
      if($action == 'add'){
          $errors = "";
          if(!(isset($_POST["rw_name"]) && trim($_POST["rw_name"] != ""))){
              $errors .= "Title Is Required.<br/>";
          }
          
          if(!(isset($_POST["rw_staus"]) && trim($_POST["rw_staus"] != ""))){
              $errors .= "Status Is Required.<br/>";
          }

          $selectunitname2 = mysqli_query($link,"select * from tp_readwritedrive where rw_name ='".$_POST["rw_name"]."'");
          if(mysqli_num_rows($selectunitname2)>0){
              $errors .= "Title Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }

          $update_qry = "INSERT INTO `tp_readwritedrive`(`rw_name`,`rw_export_excel_id`,`rw_export_sheet_id`,`rw_staus`) VALUES ('".mysqli_real_escape_string($link,$_POST['rw_name'])."','".$_POST['rw_export_excel_id']."','".$_POST['rw_export_sheet_id']."','".$_POST['rw_staus']."')";  
          $run_upd = mysqli_query($link,$update_qry); 
          $lastid = mysqli_insert_id($link);
          
          for($i=0;$i<count($_REQUEST["rwf_excel_id"]);$i++){
                $rwf_excel_id = isset($_REQUEST["rwf_excel_id"][$i]) ? $_REQUEST["rwf_excel_id"][$i] : "";
                $rwf_sheet_name = isset($_REQUEST["rwf_sheet_name"][$i]) ? $_REQUEST["rwf_sheet_name"][$i] : "";
                
                mysqli_query($link,"insert into `tp_readwritedrive_file` (`rwf_rw_id`,`rwf_excel_id`,`rwf_sheet_name`) VALUES ('".$lastid."','".$rwf_excel_id."','".$rwf_sheet_name."')");
          }
          
          $_SESSION["msg"] = "<div class='alert alert-success'>Read & Write Drive ".ucfirst(mysqli_real_escape_string($link,$_POST['rw_name']))." Inserted Successfully.</div>";
          header("location:readwritedrive.php");
          exit();

      }else if($action == 'edit'){
          $errors = "";

          if(!(isset($_POST["rw_name"]) && trim($_POST["rw_name"] != ""))){
              $errors .= "Title Is Required.<br/>";
          }

          if(!(isset($_POST["rw_staus"]) && trim($_POST["rw_staus"] != ""))){
              $errors .= "Status Is Required.<br/>";
          }
          
          $selectunitname = mysqli_query($link,"select * from tp_readwritedrive where rw_name ='".$_POST["rw_name"]."' and rw_id!='".$_POST["id"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Title Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }

          $update_qry = "UPDATE `tp_readwritedrive` SET `rw_name`='".mysqli_real_escape_string($link,$_POST["rw_name"])."',`rw_export_excel_id`='".$_POST['rw_export_excel_id']."',`rw_export_sheet_id`='".$_POST['rw_export_sheet_id']."',`rw_staus`='".$_POST['rw_staus']."' WHERE `rw_id`='".$_POST["id"]."'";

          $run_upd = mysqli_query($link,$update_qry);
          $lastid = $_POST["id"];
          mysqli_query($link,"delete from tp_readwritedrive_file where rwf_rw_id='".$lastid."'");
          for($i=0;$i<count($_REQUEST["rwf_excel_id"]);$i++){
                $rwf_excel_id = isset($_REQUEST["rwf_excel_id"][$i]) ? $_REQUEST["rwf_excel_id"][$i] : "";
                $rwf_sheet_name = isset($_REQUEST["rwf_sheet_name"][$i]) ? $_REQUEST["rwf_sheet_name"][$i] : "";
                
                mysqli_query($link,"insert into `tp_readwritedrive_file` (`rwf_rw_id`,`rwf_excel_id`,`rwf_sheet_name`) VALUES ('".$lastid."','".$rwf_excel_id."','".$rwf_sheet_name."')");
          }
          
          $_SESSION["msg"] = "<div class='alert alert-success'>Read & Write Drive ".ucfirst(mysqli_real_escape_string($link,$_POST['rw_name']))." Updated Successfully.</div>";
          header("location:readwritedrive_add.php?id=".$_POST["id"]);
          exit();

      }else if($action == 'delete'){ 
          $update_qry = "delete from tp_readwritedrive where rw_id='".$_POST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $update_qry2 = "delete from tp_readwritedrive_file where rwf_rw_id='".$_POST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry2); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Read & Write Drive Deleted Successfully.</div>";
          echo "readwritedrive.php";
      }else{
          $_SESSION["msg"] = "<div class='alert alert-danger'>Action not found.</div>";
          header("location:readwritedrive.php");
          exit();
      }
?>