<!DOCTYPE html>
<html>
<head>
<?php
        $page_name = "Query";
        include_once("inc/head.php");
        if(!isset($_SESSION['tp_app']) && $_SESSION['tp_app'] == ""){
            header("location:index.php");
            exit();
        }
        
        $selectpackunit = mysqli_query($link,"select * from tp_query ORDER BY tp_q_id DESC");
        $nmselectpackunit = mysqli_num_rows($selectpackunit);
?>
</head>
<body>
      <?php include_once("inc/top-pan.php"); ?>
      <main id="main" class="main">
          <div class="pagetitle">
              <h1>Query</h1>
              <nav>
                  <ol class="breadcrumb">
                      <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>dashboard.php">Dashboard</a></li>
                      <li class="breadcrumb-item">Query</li>
                  </ol>
              </nav>
          </div>
          <section class="section">
              <?php if(isset($_SESSION["msg"])){echo '<div class="container-fluid">'.$_SESSION["msg"].'</div>';}unset($_SESSION["msg"]); ?>
              <div class="row"><div class="col-lg-12"><div class="float-end mb-3"><a class="btn btn-primary" href="query_add.php">Add Query</a></div></div></div>
              <div class="row">
                  <div class="col-lg-12">
                      <div class="card">
                          <div class="card-body">
                              <h5 class="card-title">Query</h5>
                              <table class="table datatable">
                                  <thead>
                                      <tr>
                                          <th>ID</th>
                                          <th>Type</th>
                                          <th>Title</th>
                                          <th>Action</th>
                                      </tr>
                                  </thead>
                                  <tbody>
                                      <?php if($nmselectpackunit > 0){ 
                                              while($rwselectpackunit = mysqli_fetch_array($selectpackunit)){
                                      ?>
                                                <tr>
                                                  <td><?php echo $rwselectpackunit['tp_q_id']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tp_q_type']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tp_q_title']; ?></td>
                                                  <td>
                                                    <a class="btn btn-sm btn-primary" href="query_add.php?id=<?php echo $rwselectpackunit['tp_q_id']; ?>"><i class="bi bi-eject"></i></a>
                                                    <a class="btn btn-sm btn-danger delete_query" href="javascript:void(0)"  data-id="<?php echo $rwselectpackunit['tp_q_id']; ?>"><i class="bi bi-trash" ></i></a>
                                                  </td>
                                                </tr>
                                      <?php    }
                                            }else{ ?>
                                              <tr>
                                                <td class="text-danger text-center" colspan="4">No Query Found.</td>
                                              </tr>
                                      <?php } ?>
                                  </tbody>
                              </table>
                          </div>
                      </div>
                  </div>
              </div>
          </section>
      </main>
      <?php include_once("inc/footer.php"); ?>
      <?php include_once("js.php"); ?>
      <script>
          jQuery(document).ready(function(){
              jQuery(document).on('click','.delete_query',function(){
                  var dataid = jQuery(this).data("id");
                  if (confirm("Are you sure you want to delete this query?") == true) {
                      $.ajax({
                        type:'post',
                        url:base_url+'query_db.php',
                        data:"action=delete&id="+dataid,
                        success:function(url){
                          window.location.href = url;
                          return false;
                        },
                      });
                  }
              });
          });
      </script>
</body>
</html>