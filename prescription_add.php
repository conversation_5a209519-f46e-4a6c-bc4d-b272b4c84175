<!DOCTYPE html>
<html>
<head>
<?php
        if (isset ($_REQUEST['id']) ) {  
            $page_name = "Edit Prescription";
        }else{
            $page_name = "Add Prescription";
        }
        include_once("inc/head.php");
        if(!isset($_SESSION['tp_app']) && $_SESSION['tp_app'] == ""){
            header("location:index.php");
            exit();
        }
        if (isset ($_REQUEST['id']) ) { 
            $selectpackunit = mysqli_query($link,"select * from tp_prescription where tp_p_id='".$_REQUEST['id']."'");
            $rwselectpackunit = mysqli_fetch_array($selectpackunit);
            $action = "edit";
            $id = $_REQUEST['id'];
            $tp_prescription_title = $rwselectpackunit['tp_prescription_title'];
            $tp_p_parentname = $rwselectpackunit['tp_p_parentname'];
            $tp_p_childnodename = $rwselectpackunit['tp_p_childnodename'];
            $tp_excel_id = $rwselectpackunit['tp_excel_id'];
            $tp_sheet_id = $rwselectpackunit['tp_sheet_id'];
        }else{
            $action = "add";
            $id = "";
            $tp_prescription_title = "FLOW STARTING QUESTION BLOCK";
            $tp_p_parentname = "Flow Starting Question Bank";
            $tp_p_childnodename = "";
            $tp_excel_id = "";
            $tp_sheet_id = "";
        }
        
        function generate_nodemainaa_array($nodeoption, &$counter = 1) {
            $result = [];
            if (is_array($nodeoption)) {
                foreach ($nodeoption as $key => $val) {
                    $result[] = str_pad($counter, 5, '0', STR_PAD_LEFT);
                    $counter++;
                }
            }
            return $result;
        }
        
        function recuringnode($counterdata,$nodearray,$node,$nodeoptionarray,$nodeoption,$nodeendarray,$nodeend,$nodeendoptionarray,$nodeendoption,$nodemainaaarray,$nodemainaa,$nodemainaa2array,$nodemainaa2,$nodegenderarray,$nodegender,$nodecolorarray,$nodecolor, &$autoinc = 2) {
            $dataset = '';
            if(isset($nodeoptionarray)){
                $dataset .= '<table class="sub_node" cellpadding="0" cellspacing="0"><tbody><tr>';
                                $i=0;
                                foreach ($nodeoptionarray as $nodeoptionkey => $nodeoptionvalue) {
                                    $nodemainaa_val = isset($nodemainaaarray[$i]) ? $nodemainaaarray[$i] : str_pad($autoinc, 5, '0', STR_PAD_LEFT);
                                    $autoinc++;
                                    
                                    // Get the selected gender value
                                    $selected_gender = isset($nodegenderarray[$i]) ? $nodegenderarray[$i] : 'both';
                                    
                                    // Get the selected color value
                                    $selected_color = isset($nodecolorarray[$i]) ? $nodecolorarray[$i] : '#ffffff';
                                    
                                    $dataset .= '<td>
                                                    <div class="sub_node_box">
                                                        <div class="header_top_section">
                                                            <label>'.$nodeoptionvalue.'</label>
                                                            <input type="text" name="nodeoption['.$counterdata.'][]" class="nodeoptiontxtchange" value="'.$nodeoptionvalue.'" style="display:none" />
                                                            <input type="tel" name="nodemainaa['.$counterdata.'][]" value="'.$nodemainaa_val.'" class="form-control" style="width: 125px;margin: 20px auto;"  />
                                                        </div>
                                                        <div class="header_section">
                                                            <div class="title_section">
                                                                <label>'.$nodearray[$i].'</label>
                                                                <input type="text" name="node['.$counterdata.'][]" class="nodetxtchange" value="'.$nodearray[$i].'" style="display:none" />
                                                            </div>
                                                            <div class="color_section" style="margin: 10px 0;">
                                                                <label style="font-size: 12px; color: #666; margin-bottom: 5px; display: block;">Node Color:</label>
                                                                <input type="color" name="nodecolor['.$counterdata.']['.$i.']" value="'.$selected_color.'" class="node-color-picker" style="width: 40px; height: 30px; border: none; border-radius: 4px; cursor: pointer;" onchange="updateNodeColor(this, \''.$counterdata.$i.'\')" />
                                                            </div>
                                                            <div class="gender_section" style="margin: 10px 0;">
                                                                <div class="form-check form-check-inline">
                                                                    <input class="form-check-input" type="radio" name="nodegender['.$counterdata.']['.$i.']" value="male" '.($selected_gender == 'male' ? 'checked' : '').' onchange="jQuery(\'#add_bottom\').removeAttr(\'disabled\')">
                                                                    <label class="form-check-label">Male</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input class="form-check-input" type="radio" name="nodegender['.$counterdata.']['.$i.']" value="female" '.($selected_gender == 'female' ? 'checked' : '').' onchange="jQuery(\'#add_bottom\').removeAttr(\'disabled\')">
                                                                    <label class="form-check-label">Female</label>
                                                                </div>
                                                                <div class="form-check form-check-inline">
                                                                    <input class="form-check-input" type="radio" name="nodegender['.$counterdata.']['.$i.']" value="both" '.($selected_gender == 'both' ? 'checked' : '').' onchange="jQuery(\'#add_bottom\').removeAttr(\'disabled\')">
                                                                    <label class="form-check-label">Both</label>
                                                                </div>
                                                            </div>
                                                            <div class="action_section">
                                                                <a href="javascript:void(0);" class="add_sub_node" data-id="'.$counterdata.$i.'"><i class="bi-plus-circle"></i></a>
                                                                <a href="javascript:void(0);" class="del_sub_node"><i class="bi-trash"></i></a>
                                                                <a href="javascript:void(0);" class="dead_sub_node" data-id="'.$counterdata.$i.'"><img src="'.SITE_ROOT_ADMIN.'img/end.png"></a>
                                                                <a href="javascript:void(0);" class="add_multiple_questions btn btn-sm btn-info" data-id="'.$counterdata.$i.'" title="Multiple Questions">+M</a>
                                                                <a href="javascript:void(0);" class="collapse_node" data-id="'.$counterdata.$i.'" title="Collapse/Expand"><i class="bi-chevron-down"></i></a>
                                                            </div>
                                                        </div>
                                                        <div class="include_section include_section'.$counterdata.$i.'" data-id="'.$counterdata.$i.'">';
                                                            if(!empty($nodeend[$counterdata.$i]) || !empty($nodeendoption[$counterdata.$i])){
                                                                $dataset .= '<table class="end_node" cellpadding="0" cellspacing="0">
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td>
                                                                                            <div class="sub_node_box">
                                                                                                <div class="header_top_section">
                                                                                                    <label>'.$nodeendoption[$counterdata.$i][0].'</label>
                                                                                                    <input type="text" name="nodeendoption['.$counterdata.$i.'][]" class="nodeendtxtchange" value="'.$nodeendoption[$counterdata.$i][0].'" style="display:none" />
                                                                                                </div>
                                                                                                <div class="header_section">
                                                                                                    <div class="title_section">
                                                                                                        <label>'.$nodeend[$counterdata.$i][0].'</label>
                                                                                                        <input type="text" name="nodeend['.$counterdata.$i.'][]" class="endnodetxtchange" value="'.$nodeend[$counterdata.$i][0].'" style="display:none" />
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>';
                                                            }else{
                                                                $dataset .= recuringnode($counterdata.$i,$node[$counterdata.$i],$node,$nodeoption[$counterdata.$i],$nodeoption,$nodeend[$counterdata.$i],$nodeend,$nodeendoption[$counterdata.$i],$nodeendoption,$nodemainaa[$counterdata.$i],$nodemainaa,$nodemainaa2[$counterdata.$i],$nodemainaa2,$nodegender[$counterdata.$i],$nodegender,$nodecolor[$counterdata.$i],$nodecolor);
                                                            }
                                            $dataset .= '</div>
                                                    </div>
                                                </td>';
                                    
                                  
                                    $i++;
                                }
                $dataset .= '</tr></tbody></table>';
            }
            return $dataset;
        }
?>

</head>
<body>
    <?php include_once("inc/top-pan.php"); ?>
    <main id="main" class="main">
        <div class="pagetitle">
            <h1><?php echo $page_name; ?></h1>
            <nav>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>dashboard.php">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>prescription.php">Prescription</a></li>
                    <li class="breadcrumb-item"><?php if($action == "edit"){echo "Edit";}else{echo "Add";} ?> Prescription</li>
                </ol>
            </nav>
        </div>
        <section class="section">
            <?php if(isset($_SESSION["msg"])){echo '<div class="container-fluid">'.$_SESSION["msg"].'</div>';}unset($_SESSION["msg"]); ?>
            <div class="row">
                <div class="col-lg-12">
                    <div class="float-end mb-3">
                        <a class="btn btn-primary" href="prescription.php">Back</a>
                        <?php if($action == "edit"){ ?>
                            <a class="btn btn-primary" style="margin-left: 15px;" href="prescription_export_clean.php?id=<?php echo $id; ?>">Export Excel</a>
                            <a class="btn btn-primary" style="margin-left: 15px; display:none;" target="_blank" href="prescription_debug.php?id=<?php echo $id; ?>">Debug Html</a>
                            <?php } ?>
                        <?php if($action == "edit"){ ?>
                                <a class="btn btn-primary" href="prescription_export_to_drive_clean.php?exportiddata=<?php echo $id; ?>" style="margin-left: 15px;">Export To Drive</a>
                        <?php } ?>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Prescription Form</h5>
                            <form id="change_profile" name="change_profile" action="prescription_db.php" method="post" autocomplete="off">
                                <div class="row mb-3">
                                    <label for="tp_prescription_title" class="col-sm-3 col-form-label">Name <span>*</span></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_prescription_title" name="tp_prescription_title" placeholder="Enter Name" value="<?php echo $tp_prescription_title; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_excel_id" class="col-sm-3 col-form-label">Excel ID</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_excel_id" name="tp_excel_id" placeholder="Enter Excel ID" value="<?php echo $tp_excel_id; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <label for="tp_sheet_id" class="col-sm-3 col-form-label">Sheet ID</label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" id="tp_sheet_id" name="tp_sheet_id" placeholder="Enter Sheet ID" value="<?php echo $tp_sheet_id; ?>">
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-12">
                                        <div class="workflow_data">
                                            <!-- Zoom Controls -->
                                            <div class="workflow_controls">
                                                <button type="button" class="btn" id="zoom-in" title="Zoom In (Ctrl + =)">
                                                    <i class="bi-plus"></i>
                                                </button>
                                                <button type="button" class="btn" id="zoom-out" title="Zoom Out (Ctrl + -)">
                                                    <i class="bi-dash"></i>
                                                </button>
                                                <button type="button" class="btn" id="zoom-reset" title="Reset Zoom (Ctrl + 0)">
                                                    <i class="bi-arrow-clockwise"></i>
                                                </button>
                                                <button type="button" class="btn" id="drag-toggle" title="Toggle Drag Mode">
                                                    <i class="bi-arrows-move"></i>
                                                </button>
                                                <button type="button" class="btn" id="fullscreen-toggle" title="Toggle Fullscreen (F11)">
                                                    <i class="bi-arrows-fullscreen"></i>
                                                </button>
                                                <button type="button" class="btn" id="collapse-all" title="Collapse All Nodes">
                                                    <i class="bi-chevron-up"></i>
                                                </button>
                                                <button type="button" class="btn" id="expand-all" title="Expand All Nodes">
                                                    <i class="bi-chevron-down"></i>
                                                </button>
                                            </div>
                                            
                                            <!-- Zoom Info Display -->
                                            <div class="zoom-info" id="zoom-info" style="display: none;">
                                                Zoom: <span id="zoom-level">100%</span>
                                            </div>
                                            
                                            <div id="tree">
                                                <?php if($action == "edit"){ 
                                                        $dataf = explode("@@@@@",$tp_p_childnodename);
                                                        $node = unserialize($dataf[0]);
                                                        $nodeoption = unserialize($dataf[1]);
                                                        $nodeend = unserialize($dataf[2]);
                                                        $nodeendoption = unserialize($dataf[3]);
                                                        $nodemainaa = unserialize($dataf[4]);
                                                        $nodemainaa2 = unserialize($dataf[5]);
                                                        $nodegender = isset($dataf[6]) ? unserialize($dataf[6]) : array();
                                                        $mainnodegender = isset($dataf[7]) ? unserialize($dataf[7]) : array();
                                                        $nodecolor = isset($dataf[8]) ? unserialize($dataf[8]) : array();
                                                        $mainnodecolor = isset($dataf[9]) ? unserialize($dataf[9]) : '#ffffff';
                                                        $multiple_questions = isset($dataf[10]) ? unserialize($dataf[10]) : array();
                                                ?>      
                                                            <table class="main_node" cellpadding="0" cellspacing="0">
                                                                <tr>
                                                                    <td>
                                                                        <div class="main_node_box">
                                                                            <div class="header_section main_single_section">
                                                                                <div class="title_section">
                                                                                    <label><?php echo $tp_p_parentname; ?></label>
                                                                                    <input type="text" name="nodemain" class="nodetxtchange" value="<?php echo $tp_p_parentname; ?>" style="display:none" />
                                                                                    <input type="tel" name="nodemainaa" value="00001" class="form-control" style="width: 125px;margin: 20px auto;" />
                                                                                </div>
                                                                                <div class="color_section" style="margin: 10px 0;">
                                                                                    <label style="font-size: 12px; color: #666; margin-bottom: 5px; display: block;">Node Color:</label>
                                                                                    <input type="color" name="mainnodecolor" value="<?php echo $mainnodecolor; ?>" class="node-color-picker" style="width: 40px; height: 30px; border: none; border-radius: 4px; cursor: pointer;" onchange="updateNodeColorMain(this, '0')" />
                                                                                </div>
                                                                                <div class="gender_section" style="margin: 10px 0;">
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="male" <?php echo (isset($mainnodegender) && $mainnodegender == 'male') ? 'checked' : ''; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Male</label>
                                                                                    </div>
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="female" <?php echo (isset($mainnodegender) && $mainnodegender == 'female') ? 'checked' : ''; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Female</label>
                                                                                    </div>
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="both" <?php echo (isset($mainnodegender) && $mainnodegender == 'both') ? 'checked' : 'checked'; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Both</label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="action_section">
                                                                                    <a href="javascript:void(0);" class="add_sub_node" data-id="0"><i class="bi-plus-circle"></i></a>
                                                                                    <a href="javascript:void(0);" class="add_multiple_questions btn btn-sm btn-info" data-id="0" title="Multiple Questions">+M</a>
                                                                                    <a href="javascript:void(0);" class="collapse_node" data-id="0" title="Collapse/Expand"><i class="bi-chevron-down"></i></a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="include_section include_section0" data-id="0">
                                                                            <?php echo recuringnode(0,$node[0],$node,$nodeoption[0],$nodeoption,$nodeend[0],$nodeend,$nodeendoption[0],$nodeendoption,$nodemainaa[0],$nodemainaa,$nodemainaa2[0],$nodemainaa2,$nodegender[0],$nodegender,$nodecolor[0],$nodecolor); ?>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                <?php   }else{ 
                                                        $main_nodemainaa = '00001';
                                                ?>
                                                            <table class="main_node" cellpadding="0" cellspacing="0">
                                                                <tr>
                                                                    <td>
                                                                        <div class="main_node_box">
                                                                            <div class="header_section main_single_section">
                                                                                <div class="title_section">
                                                                                    <label><?php echo $tp_p_parentname; ?></label>
                                                                                    <input type="text" name="nodemain" class="nodetxtchange" value="<?php echo $tp_p_parentname; ?>" style="display:none" />
                                                                                    <input type="tel" name="nodemainaa" value="<?php echo $main_nodemainaa; ?>" class="form-control" style="width: 125px;margin: 20px auto;"  />
                                                                                </div>
                                                                                <div class="color_section" style="margin: 10px 0;">
                                                                                    <label style="font-size: 12px; color: #666; margin-bottom: 5px; display: block;">Node Color:</label>
                                                                                    <input type="color" name="mainnodecolor" value="#ffffff" class="node-color-picker" style="width: 40px; height: 30px; border: none; border-radius: 4px; cursor: pointer;" onchange="updateNodeColorMain(this, '0')" />
                                                                                </div>
                                                                                <div class="gender_section" style="margin: 10px 0;">
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="male" onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Male</label>
                                                                                    </div>
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="female" onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Female</label>
                                                                                    </div>
                                                                                    <div class="form-check form-check-inline">
                                                                                        <input class="form-check-input" type="radio" name="mainnodegender" value="both" checked onchange="jQuery('#add_bottom').removeAttr('disabled')">
                                                                                        <label class="form-check-label">Both</label>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="action_section">
                                                                                    <a href="javascript:void(0);" class="add_sub_node" data-id="0"><i class="bi-plus-circle"></i></a>
                                                                                    <a href="javascript:void(0);" class="collapse_node" data-id="0" title="Collapse/Expand"><i class="bi-chevron-down"></i></a>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="include_section include_section0" data-id="0"></div>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-12">
                                        <input type="hidden" name="action" id="action" value="<?php echo $action; ?>">
                                        <input type="hidden" name="id" id="id" value="<?php echo $id; ?>">
                                        <button type="submit" class="btn btn-primary" id="add_bottom" disabled="disabled">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <?php include_once("inc/footer.php"); ?>
    <?php include_once("js.php"); ?>
    <script>
        jQuery('#tp_prescription_title,#tp_excel_id,#tp_sheet_id').on("keyup change",function(){
            jQuery("#add_bottom").removeAttr('disabled');
            jQuery("#tp_prescription_title").removeClass("error-border");
        });
        jQuery("#add_bottom").click(function () {
            lfg = 1;
            var tp_prescription_title = jQuery.trim(jQuery("#tp_prescription_title").val());
            if (tp_prescription_title == "") {
                lfg = 0;
                jQuery("#tp_prescription_title").addClass("error-border");
                jQuery("#tp_prescription_title").focus();
                return false;
            }
        });
		$(document).ready(function(){
			// Initialize collapse buttons for existing nodes
			setTimeout(function() {
				initializeCollapseButtons();
				// Apply saved colors to node backgrounds
				applySavedColors();
			}, 100);
			
			$(document).on('click','.title_section',function(){
			    $(this).children("label").hide();
			    $(this).children("input[type='text']").show();
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			$(document).on('click','.exporttoexporttodrive',function(){
			    $("#exporttodrive").modal('show');
			});
			$(document).on('change','.nodetxtchange',function(){
			    $(this).parent(".title_section").children("label").html($(this).val());
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			
			// Gender radio button validation - ensure one option is always selected
			$(document).on('change', 'input[type="radio"][name*="nodegender"], input[type="radio"][name="mainnodegender"]', function(){
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			
			// Collapse/Expand functionality
			$(document).on('click','.collapse_node',function(){
			    var dataid = $(this).data("id");
			    var includeSection = $(".include_section"+dataid);
			    var collapseIcon = $(this).find('i');
			    var subNodeBox = $(this).closest('.sub_node_box');
				
				console.log('Collapse/Expand clicked for dataid:', dataid);
				console.log('Include section visible:', includeSection.is(':visible'));
				console.log('Include section display:', includeSection.css('display'));
				console.log('Include section children:', includeSection.children().length);
				
				// Check if the node is currently collapsed (has collapsed class)
				var isCurrentlyCollapsed = $(this).hasClass('collapsed');
				
				if(isCurrentlyCollapsed) {
					// Currently collapsed, so expand it
					console.log('Expanding node:', dataid);
					includeSection.slideDown(300, function() {
						// Remove collapsed class
						subNodeBox.removeClass('collapsed');
						console.log('Node expanded:', dataid);
					});
					collapseIcon.removeClass('bi-chevron-right').addClass('bi-chevron-down');
					$(this).attr('title', 'Collapse');
					$(this).removeClass('collapsed');
				} else {
					// Currently expanded, so collapse it
					console.log('Collapsing node:', dataid);
					includeSection.slideUp(300, function() {
						// Add collapsed class for styling
						subNodeBox.addClass('collapsed');
						console.log('Node collapsed:', dataid);
					});
					collapseIcon.removeClass('bi-chevron-down').addClass('bi-chevron-right');
					$(this).attr('title', 'Expand');
					$(this).addClass('collapsed');
				}
				
				jQuery("#add_bottom").removeAttr('disabled');
			});
			
			// Auto-hide collapse icon if no children
			$(document).on('click','.add_sub_node',function(){
			    var dataid = $(this).data("id");
			    var incrementnode = $(".include_section"+dataid).find('table').children("tbody").children("tr").children("td").length;
			    var last_nodemainaa = 1;
			    $('input[name^="nodemainaa"]').each(function(){
			        var val = parseInt($(this).val(), 10);
			        if(!isNaN(val) && val > last_nodemainaa) last_nodemainaa = val;
			    });

                // Get Gender of Clicked Node
                var selected_gender = 'both'; // default value
                var headerSection = $(this).closest('.header_section');
                var selectedRadio = headerSection.find('input[type="radio"][name*="nodegender"]:checked, input[type="radio"][name="mainnodegender"]:checked');
                
                if (selectedRadio.length > 0) {
                    selected_gender = selectedRadio.val();
                }

                // Get Color of Clicked Node
                var selected_color = '#ffffff'; // default value
                var colorPicker = headerSection.find('input[type="color"][name*="nodecolor"], input[type="color"][name="mainnodecolor"]');
                
                if (colorPicker.length > 0) {
                    selected_color = colorPicker.val();
                }

			    var datajp = 'datarowid='+dataid+"&totalcol="+incrementnode+"&last_nodemainaa="+last_nodemainaa+"&parent_gender="+selected_gender+"&parent_color="+selected_color;
			    $.ajax({
                    url: "data-json.php",
                    type: "post",
                    data: datajp ,
                    success: function (response) {
                        if ($(".include_section"+dataid).find('table').length > 0) {
                            $(".include_section"+dataid).children(".sub_node").children("tbody").children("tr").append(response);
                        }else{
                            $(".include_section"+dataid).append('<table class="sub_node" cellpadding="0" cellspacing="0"><tbody><tr>'+response+'</tr></tbody></table>');
                        }
                        
                        // Show collapse icon after adding children
                        var collapseBtn = $('.collapse_node[data-id="'+dataid+'"]');
                        if(collapseBtn.length) {
                            collapseBtn.show();
                            // Add has-children class to parent
                            collapseBtn.closest('.sub_node_box').addClass('has-children');
                            
                            // Check if parent was collapsed and maintain that state
                            if(collapseBtn.hasClass('collapsed')) {
                                $(".include_section"+dataid).hide();
                            }
                        }
                    }
                });
                jQuery("#add_bottom").removeAttr('disabled');
			});
			
			// Handle delete node to update collapse button visibility
			$(document).on('click','.del_sub_node',function(){
			    var parentTd = $(this).parent(".action_section").parent(".header_section").parent(".sub_node_box").parent("td");
			    var dataid = $(this).closest('.sub_node_box').find('.add_sub_node').data('id');
				
				parentTd.remove();
				
				// Check if this was the last child node
				setTimeout(function() {
					var remainingChildren = $(".include_section"+dataid).find('table').children("tbody").children("tr").children("td").length;
					if(remainingChildren === 0) {
						// Hide collapse button if no children left
						$('.collapse_node[data-id="'+dataid+'"]').hide();
						$('.collapse_node[data-id="'+dataid+'"]').closest('.sub_node_box').removeClass('has-children');
					}
				}, 100);
				
				jQuery("#add_bottom").removeAttr('disabled');
			});
			
			$(document).on('click','.header_top_section',function(){
			    $(this).children("label").hide();
			    $(this).children("input[type='text']").show();
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			$(document).on('change','.nodeoptiontxtchange',function(){
			    $(this).parent(".header_top_section").children("label").html($(this).val());
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			$(document).on('click','.dead_sub_node',function(){
			    var dataid = $(this).data("id");
			    $(".include_section"+dataid).html('<table class="end_node" cellpadding="0" cellspacing="0"><tbody><tr><td><div class="sub_node_box"><div class="header_top_section"><label>End Result</label><input type="text" name="nodeendoption['+dataid+'][]" class="nodeendtxtchange" value="End Result" style="display:none" /></div><div class="header_section"><div class="title_section"><label>Next Action</label><input type="text" name="nodeend['+dataid+'][]" class="endnodetxtchange" value="Next Action" style="display:none" /></div></div></div></td></tr></tbody></table>');
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			$(document).on('change','.nodeendtxtchange',function(){
			    $(this).parent(".header_top_section").children("label").html($(this).val());
			    jQuery("#add_bottom").removeAttr('disabled');
			});
			$(document).on('change','.endnodetxtchange',function(){
			    $(this).parent(".title_section").children("label").html($(this).val());
			    jQuery("#add_bottom").removeAttr('disabled');
			});
		});
		        $('body').click(function (event) {
            var obj = $(event.target);
            obj = obj['context']; // context : clicked element inside body
            if ($(obj).attr('class') != "title_section" && $('.title_section').is(':visible') == true) {
                $(".title_section").children("input[type='text']").hide();
                $(".title_section").children("label").show();
                 jQuery("#add_bottom").removeAttr('disabled');
            }
            if ($(obj).attr('class') != "header_top_section" && $('.header_top_section').is(':visible') == true) {
                $(".header_top_section").children("input[type='text']").hide();
                $(".header_top_section").children("label").show();
                 jQuery("#add_bottom").removeAttr('disabled');
            }
        });

        // Tree Zoom and Drag Functionality
        $(document).ready(function() {
            let currentZoom = 1;
            let isDragging = false;
            let isDragMode = false;
            let startX, startY, translateX = 0, translateY = 0;
            const tree = $('#tree');
            const treeContainer = tree.find('.main_node').first();
            
            console.log('Tree container found:', treeContainer.length);
            
            // Initialize tree container
            if (treeContainer.length) {
                treeContainer.css({
                    'transform-origin': 'center center',
                    'transition': 'transform 0.05s ease'
                });
            } else {
                // Fallback: use the tree itself as container
                tree.css({
                    'transform-origin': 'center center',
                    'transition': 'transform 0.05s ease'
                });
            }

            // Zoom In
            $('#zoom-in').click(function() {
                if (currentZoom < 3) {
                    currentZoom += 0.2;
                    updateZoom();
                }
            });

            // Zoom Out
            $('#zoom-out').click(function() {
                if (currentZoom > 0.3) {
                    currentZoom -= 0.2;
                    updateZoom();
                }
            });

            // Reset Zoom
            $('#zoom-reset').click(function() {
                currentZoom = 1;
                translateX = 0;
                translateY = 0;
                updateZoom();
                updatePan();
            });

            // Toggle Drag Mode
            $('#drag-toggle').click(function() {
                isDragMode = !isDragMode;
                $(this).toggleClass('active');
                
                if (isDragMode) {
                    tree.addClass('dragging');
                    tree.css('cursor', 'grab');
                    $('.workflow_data').addClass('drag-mode-active');
                    $('#zoom-info').show();
                    showNotification('Drag mode enabled. Click and drag to move the tree.');
                    console.log('Drag mode enabled');
                } else {
                    tree.removeClass('dragging');
                    tree.css('cursor', 'default');
                    $('.workflow_data').removeClass('drag-mode-active');
                    $('#zoom-info').hide();
                    showNotification('Drag mode disabled.');
                    console.log('Drag mode disabled');
                }
            });

            // Toggle Fullscreen Mode
            let isFullscreen = false;
            let originalStyles = {};

            $('#fullscreen-toggle').click(function() {
                toggleFullscreen();
            });
            
            // Collapse All Nodes
            $('#collapse-all').click(function() {
                $('.collapse_node:visible').each(function() {
                    var dataid = $(this).data("id");
                    var includeSection = $(".include_section"+dataid);
                    var collapseIcon = $(this).find('i');
                    var subNodeBox = $(this).closest('.sub_node_box');
                    
                    // Only collapse if not already collapsed
                    if(!$(this).hasClass('collapsed')) {
                        includeSection.slideUp(200);
                        collapseIcon.removeClass('bi-chevron-down').addClass('bi-chevron-right');
                        $(this).attr('title', 'Expand');
                        $(this).addClass('collapsed');
                        subNodeBox.addClass('collapsed');
                    }
                });
                showNotification('All nodes collapsed.');
            });
            
            // Expand All Nodes
            $('#expand-all').click(function() {
                // $('.collapse_node:visible').each(function() { // this is the only expand stpe by step
                    $('.collapse_node').each(function() {
                    var dataid = $(this).data("id");
                    var includeSection = $(".include_section"+dataid);
                    var collapseIcon = $(this).find('i');
                    var subNodeBox = $(this).closest('.sub_node_box');
                    
                    // Only expand if currently collapsed
                    if($(this).hasClass('collapsed')) {
                        includeSection.slideDown(200);
                        collapseIcon.removeClass('bi-chevron-right').addClass('bi-chevron-down');
                        $(this).attr('title', 'Collapse');
                        $(this).removeClass('collapsed');
                        subNodeBox.removeClass('collapsed');
                    }
                });
                showNotification('All nodes expanded.');
            });

            function toggleFullscreen() {
                const workflowData = $('.workflow_data');
                
                if (!isFullscreen) {
                    // Enter fullscreen
                    isFullscreen = true;
                    $(this).toggleClass('active');
                    
                    // Store original styles
                    originalStyles = {
                        position: workflowData.css('position'),
                        top: workflowData.css('top'),
                        left: workflowData.css('left'),
                        width: workflowData.css('width'),
                        height: workflowData.css('height'),
                        zIndex: workflowData.css('z-index'),
                        borderRadius: workflowData.css('border-radius')
                    };
                    
                    // Add fullscreen class
                    workflowData.addClass('fullscreen');
                    
                    // Create and show exit button
                    if (!$('.fullscreen-exit').length) {
                        $('body').append(`
                            <button class="fullscreen-exit" title="Exit Fullscreen (Esc)">
                                <i class="bi-x-lg"></i>
                            </button>
                        `);
                    }
                    $('.fullscreen-exit').addClass('active');
                    
                    // Create overlay
                    if (!$('.fullscreen-overlay').length) {
                        $('body').append('<div class="fullscreen-overlay"></div>');
                    }
                    $('.fullscreen-overlay').addClass('active');
                    
                    // Hide body scroll
                    $('body').css('overflow', 'hidden');
                    
                    showNotification('Fullscreen mode enabled. Press Esc to exit.');
                    console.log('Fullscreen enabled');
                    
                } else {
                    // Exit fullscreen
                    isFullscreen = false;
                    $(this).removeClass('active');
                    
                    // Remove fullscreen class
                    workflowData.removeClass('fullscreen');
                    
                    // Hide exit button and overlay
                    $('.fullscreen-exit').removeClass('active');
                    $('.fullscreen-overlay').removeClass('active');
                    
                    // Restore body scroll
                    $('body').css('overflow', '');
                    
                    showNotification('Fullscreen mode disabled.');
                    console.log('Fullscreen disabled');
                }
            }

            // Exit fullscreen on button click
            $(document).on('click', '.fullscreen-exit', function() {
                toggleFullscreen();
                $('#fullscreen-toggle').removeClass('active');
            });

            // Exit fullscreen on overlay click
            $(document).on('click', '.fullscreen-overlay', function(e) {
                if (e.target === this) {
                    toggleFullscreen();
                    $('#fullscreen-toggle').removeClass('active');
                }
            });

            // Mouse wheel zoom
            tree.on('wheel', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    const delta = e.originalEvent.deltaY > 0 ? -0.1 : 0.1;
                    const newZoom = Math.max(0.3, Math.min(3, currentZoom + delta));
                    currentZoom = newZoom;
                    updateZoom();
                }
            });

            // Mouse events for dragging
            tree.on('mousedown', function(e) {
                console.log('Mouse down event:', { isDragMode: isDragMode, target: e.target });
                if (isDragMode) {
                    e.preventDefault();
                    e.stopPropagation();
                    isDragging = true;
                    startX = e.clientX - translateX;
                    startY = e.clientY - translateY;
                    tree.css('cursor', 'grabbing');
                    
                    // Remove transitions during drag for faster response
                    const container = treeContainer.length ? treeContainer : tree;
                    container.addClass('dragging').css('transition', 'none');
                    
                    console.log('Drag started:', { isDragMode: isDragMode, startX: startX, startY: startY });
                }
            });

            $(document).on('mousemove', function(e) {
                if (isDragging && isDragMode) {
                    e.preventDefault();
                    e.stopPropagation();
                    translateX = e.clientX - startX;
                    translateY = e.clientY - startY;
                    updatePanFast();
                }
            });

            $(document).on('mouseup', function() {
                if (isDragging) {
                    isDragging = false;
                    
                    // Restore transitions after drag
                    const container = treeContainer.length ? treeContainer : tree;
                    container.removeClass('dragging').css('transition', 'transform 0.05s ease');
                    
                    if (isDragMode) {
                        tree.css('cursor', 'grab');
                    } else {
                        tree.css('cursor', 'default');
                    }
                    console.log('Drag ended');
                }
            });

            // Touch events for mobile
            let touchStartX, touchStartY;

            tree.on('touchstart', function(e) {
                if (isDragMode) {
                    const touch = e.originalEvent.touches[0];
                    touchStartX = touch.clientX - translateX;
                    touchStartY = touch.clientY - translateY;
                    
                    // Remove transitions during drag for faster response
                    const container = treeContainer.length ? treeContainer : tree;
                    container.addClass('dragging').css('transition', 'none');
                    
                    console.log('Touch drag started');
                }
            });

            tree.on('touchmove', function(e) {
                if (isDragMode) {
                    e.preventDefault();
                    const touch = e.originalEvent.touches[0];
                    translateX = touch.clientX - touchStartX;
                    translateY = touch.clientY - touchStartY;
                    updatePanFast();
                }
            });

            tree.on('touchend', function() {
                if (isDragMode) {
                    // Restore transitions after drag
                    const container = treeContainer.length ? treeContainer : tree;
                    container.removeClass('dragging').css('transition', 'transform 0.05s ease');
                }
            });

            // Update zoom function
            function updateZoom() {
                const container = treeContainer.length ? treeContainer : tree;
                if (container.length) {
                    container.css('transform', `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`);
                }
                $('#zoom-level').text(Math.round(currentZoom * 100) + '%');
                
                // Update button states
                $('#zoom-in').toggleClass('disabled', currentZoom >= 3);
                $('#zoom-out').toggleClass('disabled', currentZoom <= 0.3);
                
                // Show zoom info if zoomed or in drag mode
                if (currentZoom !== 1 || isDragMode) {
                    $('#zoom-info').show();
                    let infoText = `Zoom: ${Math.round(currentZoom * 100)}%`;
                    if (isDragMode) {
                        infoText += ' | Drag Mode: ON';
                    }
                    $('#zoom-level').text(infoText);
                } else {
                    $('#zoom-info').hide();
                }
                
                // Add visual feedback animation
                $('.workflow_data').addClass('zooming');
                setTimeout(() => {
                    $('.workflow_data').removeClass('zooming');
                }, 500);
            }

            // Notification function
            function showNotification(message) {
                // Create notification element
                const notification = $(`
                    <div class="notification" style="
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: linear-gradient(145deg, #9746fd, #7912f9);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 8px;
                        box-shadow: 0 4px 15px rgba(151, 70, 253, 0.3);
                        z-index: 10000;
                        font-size: 14px;
                        font-weight: 500;
                        transform: translateX(100%);
                        transition: transform 0.3s ease;
                    ">${message}</div>
                `);
                
                $('body').append(notification);
                
                // Animate in
                setTimeout(() => {
                    notification.css('transform', 'translateX(0)');
                }, 100);
                
                // Remove after 3 seconds
                setTimeout(() => {
                    notification.css('transform', 'translateX(100%)');
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            }

            // Update pan function
            function updatePan() {
                const container = treeContainer.length ? treeContainer : tree;
                if (container.length) {
                    container.css('transform', `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`);
                }
            }

            // Fast update function for drag (no transitions)
            function updatePanFast() {
                const container = treeContainer.length ? treeContainer : tree;
                if (container.length) {
                    container.css({
                        'transform': `scale(${currentZoom}) translate(${translateX}px, ${translateY}px)`,
                        'transition': 'none'
                    });
                }
            }

            // Keyboard shortcuts
            $(document).keydown(function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '=':
                        case '+':
                            e.preventDefault();
                            $('#zoom-in').click();
                            break;
                        case '-':
                            e.preventDefault();
                            $('#zoom-out').click();
                            break;
                        case '0':
                            e.preventDefault();
                            $('#zoom-reset').click();
                            break;
                    }
                }
                
                // Fullscreen shortcuts
                if (e.key === 'F11') {
                    e.preventDefault();
                    $('#fullscreen-toggle').click();
                }
                
                if (e.key === 'Escape' && isFullscreen) {
                    e.preventDefault();
                    toggleFullscreen();
                    $('#fullscreen-toggle').removeClass('active');
                }
            });

            // Double click to reset zoom
            tree.on('dblclick', function(e) {
                if (e.target === tree[0]) {
                    $('#zoom-reset').click();
                }
            });

            // Initialize
            updateZoom();
        });
        
        // Initialize collapse buttons for existing nodes
        function initializeCollapseButtons() {
            console.log('Initializing collapse buttons...');
            
            // Check each node and show/hide collapse button based on children
            $('.sub_node_box').each(function() {
                var dataid = $(this).find('.add_sub_node').data('id');
                if(dataid !== undefined) {
                    var hasChildren = $(".include_section"+dataid).find('table').length > 0;
                    var collapseBtn = $('.collapse_node[data-id="'+dataid+'"]');
                    
                    console.log('Initializing node:', dataid, 'hasChildren:', hasChildren);
                    
                    if(hasChildren) {
                        collapseBtn.show();
                        $(this).addClass('has-children');
                        
                        // Set initial state to expanded (not collapsed)
                        var includeSection = $(".include_section"+dataid);
                        collapseBtn.removeClass('collapsed');
                        collapseBtn.find('i').removeClass('bi-chevron-right').addClass('bi-chevron-down');
                        collapseBtn.attr('title', 'Collapse');
                        includeSection.show();
                        $(this).removeClass('collapsed');
                        
                        console.log('Node', dataid, 'initialized as expanded');
                    } else {
                        collapseBtn.hide();
                        $(this).removeClass('has-children');
                    }
                }
            });
            
            console.log('Collapse buttons initialization complete');
        }
        
        // Function to reset collapse state (for debugging)
        function resetCollapseState() {
            $('.collapse_node').each(function() {
                var dataid = $(this).data("id");
                var includeSection = $(".include_section"+dataid);
                var collapseIcon = $(this).find('i');
                var subNodeBox = $(this).closest('.sub_node_box');
                
                // Reset to expanded state
                includeSection.show();
                collapseIcon.removeClass('bi-chevron-right').addClass('bi-chevron-down');
                $(this).attr('title', 'Collapse');
                $(this).removeClass('collapsed');
                subNodeBox.removeClass('collapsed');
            });
            console.log('Collapse state reset');
        }
        
        // Function to test collapse/expand functionality
        function testCollapseExpand() {
            console.log('Testing collapse/expand functionality...');
            $('.collapse_node:visible').each(function() {
                var dataid = $(this).data("id");
                var includeSection = $(".include_section"+dataid);
                var isCollapsed = $(this).hasClass('collapsed');
                
                console.log('Node', dataid, 'collapsed:', isCollapsed, 'visible:', includeSection.is(':visible'));
            });
        }
        
        // Function to force expand all nodes (for testing)
        function forceExpandAll() {
            $('.collapse_node:visible').each(function() {
                var dataid = $(this).data("id");
                var includeSection = $(".include_section"+dataid);
                var collapseIcon = $(this).find('i');
                var subNodeBox = $(this).closest('.sub_node_box');
                
                // Force expand
                includeSection.show();
                collapseIcon.removeClass('bi-chevron-right').addClass('bi-chevron-down');
                $(this).attr('title', 'Collapse');
                $(this).removeClass('collapsed');
                subNodeBox.removeClass('collapsed');
            });
            console.log('All nodes forced to expanded state');
        }
        
        // Function to update node color and apply to all child nodes
        function updateNodeColor(colorPicker, nodeId) {
            var color = colorPicker.value;

            var $nodeBox = jQuery(colorPicker).closest('.sub_node_box');
            var $section1 = $nodeBox.find('.header_top_section');
            var $section2 = $nodeBox.find('.header_section');

            // Apply color to current node
            $section1.css('background-color', color);
            $section2.css('background-color', color);

            // Apply color to all child nodes recursively
            applyColorToChildren(nodeId, color);

            // Enable submit button
            jQuery("#add_bottom").removeAttr('disabled');
        }

        function updateNodeColorMain(colorPicker, nodeId) {
            var color = colorPicker.value;

            var $nodeBox = jQuery(colorPicker).closest('.main_node_box');
            var $section = $nodeBox.find('.main_single_section');

            // Apply color to current node
            $section.css('background-color', color);

            // Apply color to all child nodes recursively
            applyColorToChildren(nodeId, color);

            // Enable submit button
            jQuery("#add_bottom").removeAttr('disabled');
        }
        
        // Function to apply color to all child nodes recursively
        function applyColorToChildren(parentNodeId, color) {
            var includeSection = $(".include_section" + parentNodeId);
            var childNodes = includeSection.find('.sub_node_box');
            
            childNodes.each(function() {
                var childNode = $(this);
                var SubchilldSection1 = $(this).find('.header_top_section');
                var SubchilldSection2 = $(this).find('.header_section');
                SubchilldSection1.css('backgroundColor', color);
                SubchilldSection2.css('backgroundColor', color);
                
                // Update color picker value
                var childColorPicker = childNode.find('input[type="color"]');
                if (childColorPicker.length > 0) {
                    childColorPicker.val(color);
                }
                
                // Recursively apply to grandchildren
                var childNodeId = childNode.find('.add_sub_node').data('id');
                if (childNodeId) {
                    applyColorToChildren(childNodeId, color);
                }
            });
        }
        
        // Function to apply saved colors to node backgrounds on page load
        function applySavedColors() {
            // Apply main node color
            var mainColorPicker = $('input[name="mainnodecolor"]');
            if (mainColorPicker.length > 0) {
                var mainColor = mainColorPicker.val();
                var mainNodeBox = mainColorPicker.closest('.main_node_box .header_section');
                mainNodeBox.css('backgroundColor', mainColor);
            }
            
            // Apply colors to all sub nodes
            $('.node-color-picker').each(function() {
                var colorPicker = $(this);
                var color = colorPicker.val();
                var nodeBox = colorPicker.closest('.sub_node_box');
                
                if (color && color !== '#ffffff') {

                    var SubchilldSection1 = $(nodeBox).find('.header_top_section');
                    var SubchilldSection2 = $(nodeBox).find('.header_section');
                    SubchilldSection1.css('backgroundColor', color);
                    SubchilldSection2.css('backgroundColor', color);

                    //nodeBox.css('backgroundColor', color);
                }
            });
        }
    </script>
</body>
</html>
<!-- Add this modal before the closing </body> tag -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="multipleQuestionsModal" aria-labelledby="multipleQuestionsModalLabel" style="width: 600px;">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="multipleQuestionsModalLabel">Multiple Counter Questions & Answer Blocks</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <!-- Counter Questions Section -->
        <div class="section mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Counter Questions</h6>
                <button type="button" class="btn btn-sm btn-primary" id="addCounterQuestionRow">+ Add Question</button>
            </div>
            <div id="counterQuestionsContainer">
                <div class="counter-question-item mb-4 p-3 border rounded">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Question:</label>
                        <input type="text" class="form-control question-input" placeholder="Enter your question here">
                    </div>
                    <div class="options-container">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">Options:</label>
                            <button type="button" class="btn btn-sm btn-success add-option">+ Add Option</button>
                        </div>
                        <div class="options-list">
                            <!-- Options will be added dynamically -->
                        </div>
                    </div>
                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-sm btn-danger remove-question">Remove Question</button>
                    </div>
                </div>
            </div>
        </div>

        <hr>

        <!-- Answer Blocks Section -->
        <div class="section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Answer Blocks</h6>
                <button type="button" class="btn btn-sm btn-primary" id="addAnswerBlockRow">+ Add Answer Block</button>
            </div>
            <div id="answerBlocksContainer">
                <div class="answer-block-item mb-3 p-3 border rounded">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Answer Block Title:</label>
                        <input type="text" class="form-control answer-title" placeholder="Enter answer block title">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label">Min Score (≥):</label>
                            <input type="number" class="form-control min-score" placeholder="Min score">
                        </div>
                        <div class="col-6">
                            <label class="form-label">Max Score (<):</label>
                            <input type="number" class="form-control max-score" placeholder="Max score">
                        </div>
                    </div>
                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-sm btn-danger remove-answer-block">Remove Block</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="offcanvas-footer p-3 border-top">
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-secondary flex-fill" data-bs-dismiss="offcanvas">Cancel</button>
            <button type="button" class="btn btn-primary flex-fill" id="saveMultipleQuestions">Save</button>
        </div>
    </div>
</div>

<style>
.counter-question-item {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef !important;
}

.answer-block-item {
    background-color: #fff3cd;
    border: 2px solid #ffeaa7 !important;
}

.option-item {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    position: relative;
}

.option-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.option-number {
    position: absolute;
    top: -8px;
    left: 15px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.offcanvas-footer {
    background-color: #f8f9fa;
}

.form-label.fw-bold {
    color: #495057;
    font-size: 14px;
}

/* High z-index for offcanvas to appear over fullscreen elements */
#multipleQuestionsModal {
    z-index: 99999 !important;
}

#multipleQuestionsModal.offcanvas {
    z-index: 99999 !important;
}

.offcanvas-backdrop {
    z-index: 99998 !important;
}

/* Ensure offcanvas appears over fullscreen workflow */
.workflow_data.fullscreen ~ #multipleQuestionsModal,
.workflow_data.fullscreen ~ .offcanvas-backdrop {
    z-index: 99999 !important;
}

/* Override any conflicting z-index from fullscreen elements */
.offcanvas.show {
    z-index: 99999 !important;
}

/* Ensure backdrop is below offcanvas but above fullscreen */
.offcanvas-backdrop.show {
    z-index: 99998 !important;
}
</style>

<script>
// Add +M button functionality
$(document).on('click', '.add_multiple_questions', function() {
    var nodeId = $(this).data('id');
    $('#multipleQuestionsModal').data('node-id', nodeId);
    
    // Ensure high z-index before showing
    $('#multipleQuestionsModal').css('z-index', '99999');
    
    var offcanvas = new bootstrap.Offcanvas(document.getElementById('multipleQuestionsModal'));
    offcanvas.show();
    
    // Additional z-index enforcement after show
    setTimeout(function() {
        $('#multipleQuestionsModal').css('z-index', '99999');
        $('.offcanvas-backdrop').css('z-index', '99998');
    }, 100);
});

// Override Bootstrap's offcanvas show event to ensure z-index
$('#multipleQuestionsModal').on('show.bs.offcanvas', function() {
    $(this).css('z-index', '99999');
    setTimeout(function() {
        $('.offcanvas-backdrop').css('z-index', '99998');
    }, 50);
});

// Ensure z-index is maintained during fullscreen operations
$(document).on('click', '.workflow_controls .btn', function() {
    setTimeout(function() {
        if ($('#multipleQuestionsModal').hasClass('show')) {
            $('#multipleQuestionsModal').css('z-index', '99999');
            $('.offcanvas-backdrop').css('z-index', '99998');
        }
    }, 100);
});

// Add counter question
$('#addCounterQuestionRow').click(function() {
    var questionHtml = `
        <div class="counter-question-item mb-4 p-3 border rounded">
            <div class="mb-3">
                <label class="form-label fw-bold">Question:</label>
                <input type="text" class="form-control question-input" placeholder="Enter your question here">
            </div>
            <div class="options-container">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <label class="form-label fw-bold mb-0">Options:</label>
                    <button type="button" class="btn btn-sm btn-success add-option">+ Add Option</button>
                </div>
                <div class="options-list">
                    <!-- Options will be added dynamically -->
                </div>
            </div>
            <div class="text-end mt-3">
                <button type="button" class="btn btn-sm btn-danger remove-question">Remove Question</button>
            </div>
        </div>`;
    $('#counterQuestionsContainer').append(questionHtml);
});

// Add option to question
$(document).on('click', '.add-option', function() {
    var optionsContainer = $(this).closest('.options-container').find('.options-list');
    var optionCount = optionsContainer.find('.option-item').length + 1;
    
    var optionHtml = `
        <div class="option-item">
            <div class="option-number">${optionCount}</div>
            <div class="row">
                <div class="col-8">
                    <label class="form-label">Option Title:</label>
                    <input type="text" class="form-control option-title" placeholder="Enter option title">
                </div>
                <div class="col-4">
                    <label class="form-label">Score:</label>
                    <input type="number" class="form-control option-score" placeholder="Score">
                </div>
            </div>
            <div class="text-end mt-2">
                <button type="button" class="btn btn-sm btn-outline-danger remove-option">Remove</button>
            </div>
        </div>`;
    
    optionsContainer.append(optionHtml);
    updateOptionNumbers(optionsContainer);
});

// Remove option
$(document).on('click', '.remove-option', function() {
    var optionsContainer = $(this).closest('.options-list');
    $(this).closest('.option-item').remove();
    updateOptionNumbers(optionsContainer);
});

// Update option numbers
function updateOptionNumbers(container) {
    container.find('.option-item').each(function(index) {
        $(this).find('.option-number').text(index + 1);
    });
}

// Remove question
$(document).on('click', '.remove-question', function() {
    if ($('#counterQuestionsContainer .counter-question-item').length > 1) {
        $(this).closest('.counter-question-item').remove();
    } else {
        alert('At least one question is required');
    }
});

// Add answer block
$('#addAnswerBlockRow').click(function() {
    var answerBlockHtml = `
        <div class="answer-block-item mb-3 p-3 border rounded">
            <div class="mb-3">
                <label class="form-label fw-bold">Answer Block Title:</label>
                <input type="text" class="form-control answer-title" placeholder="Enter answer block title">
            </div>
            <div class="row">
                <div class="col-6">
                    <label class="form-label">Min Score (≥):</label>
                    <input type="number" class="form-control min-score" placeholder="Min score">
                </div>
                <div class="col-6">
                    <label class="form-label">Max Score (<):</label>
                    <input type="number" class="form-control max-score" placeholder="Max score">
                </div>
            </div>
            <div class="text-end mt-3">
                <button type="button" class="btn btn-sm btn-danger remove-answer-block">Remove Block</button>
            </div>
        </div>`;
    $('#answerBlocksContainer').append(answerBlockHtml);
});

// Remove answer block
$(document).on('click', '.remove-answer-block', function() {
    if ($('#answerBlocksContainer .answer-block-item').length > 1) {
        $(this).closest('.answer-block-item').remove();
    } else {
        alert('At least one answer block is required');
    }
});

// Save multiple questions
$('#saveMultipleQuestions').click(function() {
    var nodeId = $('#multipleQuestionsModal').data('node-id');
    
    // Collect counter questions data
    var counterQuestions = [];
    $('#counterQuestionsContainer .counter-question-item').each(function() {
        var questionItem = $(this);
        var question = questionItem.find('.question-input').val();
        var options = [];
        
        questionItem.find('.option-item').each(function() {
            var optionItem = $(this);
            var title = optionItem.find('.option-title').val();
            var score = optionItem.find('.option-score').val();
            
            if (title || score) {
                options.push({title: title, score: score});
            }
        });
        
        if (question) {
            counterQuestions.push({question: question, options: options});
        }
    });
    
    // Collect answer blocks data
    var answerBlocks = [];
    $('#answerBlocksContainer .answer-block-item').each(function() {
        var blockItem = $(this);
        var title = blockItem.find('.answer-title').val();
        var minScore = blockItem.find('.min-score').val();
        var maxScore = blockItem.find('.max-score').val();
        
        if (title) {
            answerBlocks.push({
                title: title,
                minScore: minScore,
                maxScore: maxScore
            });
        }
    });
    
    // Store data in hidden inputs
    var multipleQuestionsData = {
        counterQuestions: counterQuestions,
        answerBlocks: answerBlocks
    };

    // Remove existing hidden input for this node
    $('input[name="multiple_questions[' + nodeId + ']"]').remove();

    // Add new hidden input
    var hiddenInput = $('<input type="hidden" name="multiple_questions[' + nodeId + ']" value="' + JSON.stringify(multipleQuestionsData) + '">');
    $('#change_profile').append(hiddenInput);

    // Visual feedback
    $('.add_multiple_questions[data-id="' + nodeId + '"]').addClass('btn-success').removeClass('btn-info');

    // Show success message
    if (answerBlocks.length > 0) {
        showNotification('Multiple questions saved successfully! ' + answerBlocks.length + ' answer blocks will be created as child nodes when you save the prescription.');
    } else {
        showNotification('Multiple questions saved successfully!');
    }

    // Close offcanvas
    var offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('multipleQuestionsModal'));
    offcanvas.hide();

    jQuery("#add_bottom").removeAttr('disabled');
});
</script>
