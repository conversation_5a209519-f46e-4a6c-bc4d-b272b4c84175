<!DOCTYPE html>
<html lang="en">
<head>
	<?php 
		$page_name = "Login";
		include_once("inc/head.php");
		if(isset($_SESSION['tp_app']) && $_SESSION['tp_app'] != ""){
			header("location:dashboard.php");
			exit();
		}
	?>
</head>
<body style="background:url(<?php echo SITE_ROOT_ADMIN;?>img/bg.jpg);background-repeat:no-repeat ;background-size: 100% 100%;">
	<main>
	    <div class="container">
	      	<section class="section register min-vh-100 d-flex flex-column align-items-center justify-content-center py-4">
			        <div class="container">
				          <div class="row justify-content-center">
					            <div class="col-lg-4 col-md-6 d-flex flex-column align-items-center justify-content-center">
						              <div class="card mb-3">
							                <div class="card-body">
								                  <div class="pt-4 pb-2">
								                    	<h5 class="card-title text-center pb-0 fs-4">Login to Your Account</h5>
								                    	<p class="text-center small">Enter your username & password to login</p>
								                  </div>
								                  <form class="row g-3" method="post" autocomplete="off">
									                    <div class="col-12">
									                      	<label for="username" class="form-label">Username</label>
									                      	<input type="text" class="form-control" id="username" name="username" placeholder="Username" value="<?php if(isset($_COOKIE['user_name'])){echo $_COOKIE['user_name'];} ?>">
									                    </div>
									                    <div class="col-12">
									                      	<label for="password" class="form-label">Password</label>
									                      	<input type="password" class="form-control" id="password" name="password" placeholder="Password" value="<?php if(isset($_COOKIE['password'])){echo $_COOKIE['password'];} ?>">
									                    </div>
									                    <div class="col-12">
										                      <div class="form-check">
										                      		<input type="checkbox" class="form-check-input" value="1" id="remember" <?php if(isset($_COOKIE['remember'])){echo "checked";} ?> name="remember">
										                        	<label class="form-check-label" for="remember">Keep me signed in</label>
										                      </div>
									                    </div>
									                    <div class="col-12">
									                    		<input type="submit" value="SIGN IN" id="login_btn" class="btn btn-primary w-100" />
									                    </div>
									                    <div class="col-12">
									                      	<p class="small mb-0">Are you forgot your password? <a href="<?php echo SITE_ROOT_ADMIN; ?>forgot_password.php">Forgot password?</a></p>
									                    </div>
								                  </form>
							                </div>
						              </div>
						              <div class="credits">Powered by <a href="https://www.garudatechhub.com/" target="_blank" rel="nofollow">Garuda Tech Hub</a></div>
					            </div>
				          </div>
			        </div>
	      	</section>
	    </div>
  </main>
  <?php include_once("js.php"); ?>
  <script>
  	$("#login_btn").click(function(){
				lfg = 1;
				var unm = $.trim($("#username").val());
				if(unm==""){
						lfg = 0;
						$("#user_type").removeClass("login-error");
						$("#username").addClass("login-error");
						$("#password").removeClass("login-error");
						$("#username").focus();
						return false;
				}else{
						$("#username").removeClass("login-error");
				}
				var pass = $.trim($("#password").val());
				if(pass==""){
						lfg = 0;
						$("#username").removeClass("login-error");
						$("#password").addClass("login-error");
						$("#password").focus();
						return false;
				}else{
						$("#password").removeClass("login-error");
				}
		
				var remember = $.trim($("#remember").val());
				$.ajax({
						type	: "POST",
						url		: "index_db.php",
						data	: { 
									myaction:"verification",
									unm : unm,
									pass : pass,
									remember : remember
						},
						success	: function(result){
								lfg = 0;			
								if($.trim(result)=="1"){
										alert("Username is not exists.");
										$("#username").addClass("login-error");
										$("#password").removeClass("login-error");
										$("#username").focus();
								}else if($.trim(result)=="2"){
										alert("Password is not exists.");
										$("#username").removeClass("login-error");
										$("#password").addClass("login-error");
										$("#password").focus();
								}else if($.trim(result)=="3"){
										alert("Contact admin for login.");
										$("#username").addClass("login-error");
								}else if($.trim(result)=="0"){
										$("#username").removeClass("login-error");
										$("#password").removeClass("login-error");
										window.location='dashboard.php';
								}
								return false;
						}
				});
				return false;
	});
  </script>
</body>
</html>