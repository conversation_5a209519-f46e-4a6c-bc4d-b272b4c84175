<?php

use PhpOffice\PhpSpreadsheet\Helper\Sample;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

require 'PHPExcel-1.8/vendor/autoload.php';
require 'inc/connection.php';
date_default_timezone_set('Europe/London');
$id = $_REQUEST['id']; 
$filename = "prescription_".$id.".xlsx"; 
        
$helper = new Sample();
if ($helper->isCli()) {
    $helper->log('This example should only be run from a Web Browser' . PHP_EOL);

    return;
}
// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

$dataresults = mysqli_query($link,"select * from tp_prescription where tp_p_id='".$id."'");
        $rwdataresults = mysqli_fetch_array($dataresults);

$tp_prescription_title = $rwdataresults['tp_prescription_title'];
$tp_p_parentname = $rwdataresults['tp_p_parentname'];
$tp_p_childnodename = $rwdataresults['tp_p_childnodename'];

$dataf = explode("@@@@@",$tp_p_childnodename);
$node = unserialize($dataf[0]);
$nodeoption = unserialize($dataf[1]);
$nodeend = unserialize($dataf[2]);
$nodeendoption = unserialize($dataf[3]);
$nodemainaa = unserialize($dataf[4]);
$nodemainaa2 = unserialize($dataf[5]);
$nodegender = isset($dataf[6]) ? unserialize($dataf[6]) : array();
$mainnodegender = isset($dataf[7]) ? unserialize($dataf[7]) : array();

// Helper function to get gender suffix
function getGenderSuffix($gender) {
    switch($gender) {
        case 'male': return 'M';
        case 'female': return 'F';
        case 'both': return 'B';
        default: return 'B';
    }
}

$firstcount = count($nodeoption[0]);

$sheet->setCellValue("A1", "");
$sheet->getStyle('A1')->applyFromArray(['font' => ['bold' => true,'color' => array('rgb' => 'FFFFFF'),'size'  => 11],'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,'color' => array('rgb' => '0000FF'),],]);
$sheet->getStyle('A1')->getAlignment()->setWrapText(true);
$sheet->setCellValue("B1", "FURTHER QUESTION OR FIRST QUESTION");
$sheet->getStyle('B1')->applyFromArray(['font' => ['bold' => true,'color' => array('rgb' => 'FFFFFF'),'size'  => 11],'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,'color' => array('rgb' => '0000FF'),],]);
$sheet->getStyle('B1')->getAlignment()->setWrapText(true);
$sheet->getStyle('B1')->getAlignment()->setHorizontal('center');
$sheet->getStyle('B1')->getAlignment()->setVertical('center');


for($p=0;$p<$firstcount;$p++){
    if($p==0){
        $ptitle = "C1";
    }else if($p==1){
        $ptitle = "D1";
    }else if($p==2){
        $ptitle = "E1";
    }else if($p==3){
        $ptitle = "F1";
    }else if($p==4){
        $ptitle = "G1";
    }else if($p==5){
        $ptitle = "H1";
    }else if($p==6){
        $ptitle = "I1";
    }else if($p==7){
        $ptitle = "J1";
    }else if($p==8){
        $ptitle = "K1";
    }else if($p==9){
        $ptitle = "L1";
    }
    $sheet->setCellValue($ptitle, "Option");
    $sheet->getStyle($ptitle)->applyFromArray(['font' => ['bold' => true,'color' => array('rgb' => 'FFFFFF'),'size'  => 11],'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,'color' => array('rgb' => '0000FF'),],]);
    $sheet->getStyle($ptitle)->getAlignment()->setWrapText(true);
    $sheet->getStyle($ptitle)->getAlignment()->setHorizontal('center');
    $sheet->getStyle($ptitle)->getAlignment()->setVertical('center');
}

$sheet->setCellValue('A2', 'Chart Name : '.$tp_prescription_title);
$sheet->getStyle('A2')->applyFromArray(['font' => ['bold' => true,'color' => array('rgb' => '000000'),'size'  => 11],'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,'color' => array('rgb' => 'F4CCCC'),],]);
$sheet->getStyle('A2')->getAlignment()->setWrapText(true); 
$sheet->getStyle('A2')->getAlignment()->setHorizontal('center');
$sheet->getStyle('A2')->getAlignment()->setVertical('center');
$startcell = "1";

$sheet->setCellValue('B2', $tp_p_parentname."(00001)(".getGenderSuffix($mainnodegender).")");
$sheet->getStyle('B2')->getAlignment()->setWrapText(true); 
$sheet->getStyle('B2')->getAlignment()->setHorizontal('center');
$sheet->getStyle('B2')->getAlignment()->setVertical('center');
$startcell++;

$sheet->setCellValue('C2', $nodeoption[0][0]."(".$nodemainaa[0][0].")(".getGenderSuffix($nodegender[0][0]).")");
$sheet->getStyle('C2')->getAlignment()->setWrapText(true); 
$sheet->getStyle('C2')->getAlignment()->setHorizontal('center');
$sheet->getStyle('C2')->getAlignment()->setVertical('center');

if(isset($nodeoption[0][1])){
    $startcell++;
    $sheet->setCellValue('D2', $nodeoption[0][1]."(".$nodemainaa[0][1].")(".getGenderSuffix($nodegender[0][1]).")");
    $sheet->getStyle('D2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('D2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('D2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][2])){
    $startcell++;
    $sheet->setCellValue('E2', $nodeoption[0][2]."(".$nodemainaa[0][2].")(".getGenderSuffix($nodegender[0][2]).")");
    $sheet->getStyle('E2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('E2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('E2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][3])){
    $startcell++;
    $sheet->setCellValue('F2', $nodeoption[0][3]."(".$nodemainaa[0][3].")(".getGenderSuffix($nodegender[0][3]).")");
    $sheet->getStyle('F2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('F2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('F2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][4])){
    $startcell++;
    $sheet->setCellValue('G2', $nodeoption[0][4]."(".$nodemainaa[0][4].")(".getGenderSuffix($nodegender[0][4]).")");
    $sheet->getStyle('G2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('G2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('G2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][5])){
    $startcell++;
    $sheet->setCellValue('H2', $nodeoption[0][5]."(".$nodemainaa[0][5].")(".getGenderSuffix($nodegender[0][5]).")");
    $sheet->getStyle('H2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('H2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('H2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][6])){
    $startcell++;
    $sheet->setCellValue('I2', $nodeoption[0][6]."(".$nodemainaa[0][6].")(".getGenderSuffix($nodegender[0][6]).")");
    $sheet->getStyle('I2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('I2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('I2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][7])){
    $startcell++;
    $sheet->setCellValue('J2', $nodeoption[0][7]."(".$nodemainaa[0][7].")(".getGenderSuffix($nodegender[0][7]).")");
    $sheet->getStyle('J2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('J2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('J2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][8])){
    $startcell++;
    $sheet->setCellValue('K2', $nodeoption[0][8]."(".$nodemainaa[0][8].")(".getGenderSuffix($nodegender[0][8]).")");
    $sheet->getStyle('K2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('K2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('K2')->getAlignment()->setVertical('center');
}

if(isset($nodeoption[0][9])){
    $startcell++;
    $sheet->setCellValue('L2', $nodeoption[0][9]."(".$nodemainaa[0][9].")(".getGenderSuffix($nodegender[0][9]).")");
    $sheet->getStyle('L2')->getAlignment()->setWrapText(true); 
    $sheet->getStyle('L2')->getAlignment()->setHorizontal('center');
    $sheet->getStyle('L2')->getAlignment()->setVertical('center');
}


$o = 3;
foreach($node[0] as $nodekey => $nodevalue){
    if(isset($node["0".$nodekey])){
        
        $sheet->setCellValue('A'.$o, $tp_p_parentname."(00001)(".getGenderSuffix($mainnodegender).")->".$nodeoption[0][$nodekey]."(".$nodemainaa[0][$nodekey].")(".getGenderSuffix($nodegender[0][$nodekey]).")");
        $sheet->getStyle('A'.$o)->getAlignment()->setWrapText(true); 
        $sheet->getStyle('A'.$o)->getAlignment()->setHorizontal('center');
        $sheet->getStyle('A'.$o)->getAlignment()->setVertical('center');
        
        if(isset($node[0][0])){
            $startcell++;
            $sheet->setCellValue('B'.$o, $node[0][0]."(".$nodemainaa2[0][0].")(".getGenderSuffix($nodegender["0"][0]).")");
            $sheet->getStyle('B'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('B'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('B'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][0])){
            $startcell++;
            $sheet->setCellValue('C'.$o, $nodeoption[0][0]."(".$nodemainaa["0".$nodekey][0].")(".getGenderSuffix($nodegender["0".$nodekey][0]).")");
            $sheet->getStyle('C'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('C'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('C'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][1])){
            $startcell++;
            $sheet->setCellValue('D'.$o, $nodeoption[0][1]."(".$nodemainaa["0".$nodekey][1].")(".getGenderSuffix($nodegender["0".$nodekey][1]).")");
            $sheet->getStyle('D'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('D'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('D'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][2])){
            $startcell++;
            $sheet->setCellValue('E'.$o, $nodeoption[0][2]."(".$nodemainaa["0".$nodekey][2].")(".getGenderSuffix($nodegender["0".$nodekey][2]).")");
            $sheet->getStyle('E'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('E'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('E'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][3])){
            $startcell++;
            $sheet->setCellValue('F'.$o, $nodeoption[0][3]."(".$nodemainaa["0".$nodekey][3].")(".getGenderSuffix($nodegender["0".$nodekey][3]).")");
            $sheet->getStyle('F'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('F'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('F'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][4])){
            $startcell++;
            $sheet->setCellValue('G'.$o, $nodeoption[0][4]."(".$nodemainaa["0".$nodekey][4].")(".getGenderSuffix($nodegender["0".$nodekey][4]).")");
            $sheet->getStyle('G'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('G'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('G'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][5])){
            $startcell++;
            $sheet->setCellValue('H'.$o, $nodeoption[0][5]."(".$nodemainaa["0".$nodekey][5].")(".getGenderSuffix($nodegender["0".$nodekey][5]).")");
            $sheet->getStyle('H'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('H'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('H'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][6])){
            $startcell++;
            $sheet->setCellValue('I'.$o, $nodeoption[0][6]."(".$nodemainaa["0".$nodekey][6].")(".getGenderSuffix($nodegender["0".$nodekey][6]).")");
            $sheet->getStyle('I'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('I'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('I'.$o)->getAlignment()->setVertical('center');
        }
        
        if(isset($nodeoption[0][7])){
            $startcell++;
            $sheet->setCellValue('J'.$o, $nodeoption[0][7]."(".$nodemainaa["0".$nodekey][7].")(".getGenderSuffix($nodegender["0".$nodekey][7]).")");
            $sheet->getStyle('J'.$o)->getAlignment()->setWrapText(true); 
            $sheet->getStyle('J'.$o)->getAlignment()->setHorizontal('center');
            $sheet->getStyle('J'.$o)->getAlignment()->setVertical('center');
        }
        
        $o++;
    }            
    
}

function addRecursiveRows($counterdata,$nodearray,$node,$nodeoptionarray,$nodeoption,$nodeendarray,$nodeend,$nodeendoptionarray,$nodeendoption,$sheet,$incementvalue,$startcell,$nodemainaaarray,$nodemainaa,$nodemainaa2array,$nodemainaa2,$nodegenderarray,$nodegender,$mainnodegenderarray,$mainnodegender) {
    if(isset($nodeoptionarray)){
        $i=0;
        foreach ($nodeoptionarray as $nodeoptionkey => $nodeoptionvalue) {
            if(!empty($nodeend[$counterdata.$i]) || !empty($nodeendoption[$counterdata.$i])){
                if(isset($nodeend[$counterdata.$i])){
                    $lastiddata = substr($counterdata, 0, -1);
                    for($n=0;$n<count($node[$lastiddata]);$n++){
                        if(isset($node[$lastiddata][$n]) && $node[$lastiddata][$n] != ""){
                            if(isset($node[$lastiddata.$n])){
                                
                                $sheet->setCellValue('A'.$incementvalue, $node[$lastiddata][$n]."(".$nodemainaa2[$lastiddata][$n].")(".getGenderSuffix($nodegenderarray[$n]).") ->".$nodeoption[$counterdata][0]."(".$nodemainaa[$counterdata][0].")(".getGenderSuffix($nodegender[$counterdata][0]).")");
                                $sheet->getStyle('A'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('A'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('A'.$incementvalue)->getAlignment()->setVertical('center');
                                
                                
                                $sheet->setCellValue('B'.$incementvalue, $nodeendoption[$counterdata."0"][0]);
                                $sheet->getStyle('B'.$incementvalue)->applyFromArray(['font' => ['bold' => true,'color' => array('rgb' => '000000'),'size'  => 11],'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,'color' => array('rgb' => 'F4CCCC'),],]);
                                $sheet->getStyle('B'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('B'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('B'.$incementvalue)->getAlignment()->setVertical('center');
                                //echo $counterdata."====B".$incementvalue."---------".$nodeendoption[$counterdata."0"][0];
                                //echo "<br/>";
                                $incementvalue++;
                            }
                        }
                    }
                }
            }else{
               if(isset($node[$counterdata.$i])){
                for($m=0;$m<count($nodeoption[$counterdata.$i]);$m++){
                    if(isset($node[$counterdata.$i.$m])){
                        $sheet->setCellValue('A'.$incementvalue, $nodearray[$i]."(".$nodemainaa2array[$i].")(".getGenderSuffix($nodegenderarray[$i]).")->".$nodeoption[$counterdata.$i][$m]."(".$nodemainaa[$counterdata.$i][$m].")(".getGenderSuffix($nodegender[$counterdata.$i][$m]).")");
                        $sheet->getStyle('A'.$incementvalue)->getAlignment()->setWrapText(true); 
                        $sheet->getStyle('A'.$incementvalue)->getAlignment()->setHorizontal('center');
                        $sheet->getStyle('A'.$incementvalue)->getAlignment()->setVertical('center');
                        
                        if(isset($node[$counterdata.$i][$m]) && $node[$counterdata.$i][$m] != ""){
                            $startcell++;
                            $sheet->setCellValue('B'.$incementvalue, $node[$counterdata.$i][$m]."(".$nodemainaa2[$counterdata.$i][$m].")(".getGenderSuffix($mainnodegenderarray[$m]).")");
                            $sheet->getStyle('B'.$incementvalue)->getAlignment()->setWrapText(true); 
                            $sheet->getStyle('B'.$incementvalue)->getAlignment()->setHorizontal('center');
                            $sheet->getStyle('B'.$incementvalue)->getAlignment()->setVertical('center');
                        }
                        
                        $ccount = count($nodeoption[$counterdata.$i.$m]);
                       // echo $startcell."___".$ccount;
                       // echo "<br/>";
                        $startcell = $startcell + count($nodeoption[$counterdata.$i.$m]);
                        
                        for($k=0;$k<$ccount;$k++){
                            if(isset($nodeoption[$counterdata.$i.$m][0])){
                                
                                $sheet->setCellValue('C'.$incementvalue, $nodeoption[$counterdata.$i.$m][0]."(".$nodemainaa[$counterdata.$i.$m][0].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][0]).")");
                                $sheet->getStyle('C'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('C'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('C'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][1])){
                                
                                $sheet->setCellValue('D'.$incementvalue, $nodeoption[$counterdata.$i.$m][1]."(".$nodemainaa[$counterdata.$i.$m][1].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][1]).")");
                                $sheet->getStyle('D'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('D'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('D'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][2])){
                                
                                $sheet->setCellValue('E'.$incementvalue, $nodeoption[$counterdata.$i.$m][2]."(".$nodemainaa[$counterdata.$i.$m][2].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][2]).")");
                                $sheet->getStyle('E'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('E'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('E'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][3])){
                                
                                $sheet->setCellValue('F'.$incementvalue, $nodeoption[$counterdata.$i.$m][3]."(".$nodemainaa[$counterdata.$i.$m][3].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][3]).")");
                                $sheet->getStyle('F'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('F'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('F'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][4])){
                                
                                $sheet->setCellValue('G'.$incementvalue, $nodeoption[$counterdata.$i.$m][4]."(".$nodemainaa[$counterdata.$i.$m][4].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][4]).")");
                                $sheet->getStyle('G'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('G'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('G'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][5])){
                                
                                $sheet->setCellValue('H'.$incementvalue, $nodeoption[$counterdata.$i.$m][5]."(".$nodemainaa[$counterdata.$i.$m][5].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][5]).")");
                                $sheet->getStyle('H'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('H'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('H'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][6])){
                                
                                $sheet->setCellValue('I'.$incementvalue, $nodeoption[$counterdata.$i.$m][6]."(".$nodemainaa[$counterdata.$i.$m][6].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][6]).")");
                                $sheet->getStyle('I'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('I'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('I'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            if(isset($nodeoption[$counterdata.$i.$m][7])){
                                
                                $sheet->setCellValue('J'.$incementvalue, $nodeoption[$counterdata.$i.$m][7]."(".$nodemainaa[$counterdata.$i.$m][7].")(".getGenderSuffix($nodegender[$counterdata.$i.$m][7]).")");
                                $sheet->getStyle('J'.$incementvalue)->getAlignment()->setWrapText(true); 
                                $sheet->getStyle('J'.$incementvalue)->getAlignment()->setHorizontal('center');
                                $sheet->getStyle('J'.$incementvalue)->getAlignment()->setVertical('center');
                            }
                            //echo "C".$incementvalue."---------".$nodeoption[$counterdata.$i.$m][$k];
                            //echo "<br/>";
                        }
                        //echo $counterdata.$i.$m."===="."C".$incementvalue."---------".$node[$counterdata.$i][0]."->".$nodeoption[$counterdata.$i][$m];
                        
                        $incementvalue++;
                    }
                }
                //echo "<br/><br/><br/>";
                
               }
                addRecursiveRows($counterdata.$i,$node[$counterdata.$i],$node,$nodeoption[$counterdata.$i],$nodeoption,$nodeend[$counterdata.$i],$nodeend,$nodeendoption[$counterdata.$i],$nodeendoption,$sheet,$incementvalue,$startcell,$nodemainaa[$counterdata.$i],$nodemainaa,$nodemainaa2[$counterdata.$i],$nodemainaa2,$nodegender[$counterdata.$i],$nodegender,$mainnodegenderarray,$mainnodegender);
            }
            $i++;
        }
    }
}
addRecursiveRows(0,$node[0],$node,$nodeoption[0],$nodeoption,$nodeend[0],$nodeend,$nodeendoption[0],$nodeendoption,$sheet,$o,$startcell,$nodemainaa[0],$nodemainaa,$nodemainaa2[0],$nodemainaa2,$nodegender[0],$nodegender,$mainnodegender,$mainnodegender);
//exit;
/*function addRecursiveRows($questions, $options, $currentKey, $parentQuestion, $sheet, &$row) {
    if (isset($questions[$currentKey]) && isset($options[$currentKey])) {
        foreach ($questions[$currentKey] as $key => $question) {
            $option = $options[$currentKey][$key] ?? "";
            $sheet->setCellValue("A$row", "$parentQuestion -> $option")
                  ->setCellValue("B$row", $question);
            $sheet->getStyle("A$row")->getAlignment()->setWrapText(true); 
            $sheet->getStyle("A$row")->getAlignment()->setHorizontal('center');
            $sheet->getStyle("A$row")->getAlignment()->setVertical('center');
            $sheet->getStyle("B$row")->getAlignment()->setWrapText(true); 
            $sheet->getStyle("B$row")->getAlignment()->setHorizontal('center');
            $sheet->getStyle("B$row")->getAlignment()->setVertical('center');

            // Update options for specific rows if needed
            if ($currentKey === "000" && $key === 0) {
                $sheet->setCellValue("C$row", $options["0000"][0] ?? ""); // Add 'left Option 1.4' to C7
                $sheet->getStyle("C$row")->getAlignment()->setWrapText(true); 
                $sheet->getStyle("C$row")->getAlignment()->setHorizontal('center');
                $sheet->getStyle("C$row")->getAlignment()->setVertical('center');
            }

            $row++;

            // Recurse into deeper levels
            $nextKey = $currentKey . $key;
            addRecursiveRows($questions, $options, $nextKey, $question, $sheet, $row);
        }
    }
}

addRecursiveRows($questions, $options, "00", $questions[0][0] ?? "", $sheet, $row);
addRecursiveRows($questions, $options, "01", $questions[0][1] ?? "", $sheet, $row);
addRecursiveRows($questions, $options, "02", $questions[0][2] ?? "", $sheet, $row);*/

// Rename worksheet
$spreadsheet->getActiveSheet()->setTitle('Simple');

// Set active sheet index to the first sheet, so Excel opens this as the first sheet
$spreadsheet->setActiveSheetIndex(0);

// Redirect output to a client's web browser (Xlsx)
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="Flowchart_Output_Level.xlsx"');
header('Cache-Control: max-age=0');
// If you're serving to IE 9, then the following may be needed
header('Cache-Control: max-age=1');

// If you're serving to IE over SSL, then the following may be needed
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
header('Pragma: public'); // HTTP/1.0

$writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
$writer->save('php://output');
exit;