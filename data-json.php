<?php include_once 'inc/connection.php';
      $dataid = $_POST['dataid'];
      $datarowid = $_POST['datarowid'];
      $totalcol = $_POST['totalcol'];
      
      $nodeoptionname = "nodeoption[$datarowid][]";
      $nodename = "node[$datarowid][]";
      $includesection = $datarowid.$totalcol;
     
      $last_nodemainaa = isset($_POST['last_nodemainaa']) ? intval($_POST['last_nodemainaa']) : 1;
      $new_nodemainaa = str_pad($last_nodemainaa + 1, 5, '0', STR_PAD_LEFT);
      
      // Get parent gender value
      $parent_gender = isset($_POST['parent_gender']) ? $_POST['parent_gender'] : 'both';
      
      // Get parent color value
      $parent_color = isset($_POST['parent_color']) ? $_POST['parent_color'] : '#ffffff';
      
      // Set radio button checked state based on parent value
      $male_checked = ($parent_gender == 'male') ? 'checked' : '';
      $female_checked = ($parent_gender == 'female') ? 'checked' : '';
      $both_checked = ($parent_gender == 'both') ? 'checked' : '';
     
?>
<td>
    <div class="sub_node_box">
        <div class="header_top_section" style="background-color:<?php echo $parent_color ?>">
            <label>Option</label>
            <input type="text" name="<?php echo $nodeoptionname; ?>" class="nodeoptiontxtchange" value="Option" style="display:none" />
            <input type="tel" name="nodemainaa[<?php echo $datarowid; ?>][]" value="<?php echo $new_nodemainaa; ?>" class="form-control" style="width: 125px;margin: 20px auto;" />
        </div>
        <div class="header_section" style="background-color:<?php echo $parent_color ?>">
            <div class="title_section">
                <label>Counter Question</label>
                <input type="text" name="<?php echo $nodename; ?>" class="nodetxtchange" value="Counter Question" style="display:none" />
            </div>
            <div class="color_section" style="margin: 10px 0;">
                <label style="font-size: 12px; color: #666; margin-bottom: 5px; display: block;">Node Color:</label>
                <input type="color" name="nodecolor[<?php echo $datarowid; ?>][<?php echo $totalcol; ?>]" value="<?php echo $parent_color; ?>" class="node-color-picker" style="width: 40px; height: 30px; border: none; border-radius: 4px; cursor: pointer;" onchange="updateNodeColor(this, '<?php echo $datarowid.$totalcol; ?>')" />
            </div>
            <div class="gender_section" style="margin: 10px 0;">
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="nodegender[<?php echo $datarowid; ?>][<?php echo $totalcol; ?>]" value="male" <?php echo $male_checked; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                    <label class="form-check-label">Male</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="nodegender[<?php echo $datarowid; ?>][<?php echo $totalcol; ?>]" value="female" <?php echo $female_checked; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                    <label class="form-check-label">Female</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="nodegender[<?php echo $datarowid; ?>][<?php echo $totalcol; ?>]" value="both" <?php echo $both_checked; ?> onchange="jQuery('#add_bottom').removeAttr('disabled')">
                    <label class="form-check-label">Both</label>
                </div>
            </div>
            <div class="action_section">
                <a href="javascript:void(0);" class="add_sub_node" data-id="<?php echo $includesection; ?>"><i class="bi-plus-circle"></i></a>
                <a href="javascript:void(0);" class="del_sub_node"><i class="bi-trash"></i></a>
                <a href="javascript:void(0);" class="dead_sub_node" data-id="<?php echo $includesection; ?>"><img src="<?php echo SITE_ROOT_ADMIN; ?>img/end.png"></a>
                <a href="javascript:void(0);" class="add_multiple_questions btn btn-sm btn-info" data-id="<?php echo $includesection; ?>" title="Multiple Questions">+M</a>
                <a href="javascript:void(0);" class="collapse_node" data-id="<?php echo $includesection; ?>" title="Collapse/Expand" style="display:none;"><i class="bi-chevron-down"></i></a>
            </div>
        </div>
        <div class="include_section include_section<?php echo $includesection; ?>" data-id="<?php echo $includesection; ?>"></div>
    </div>
</td>