<!DOCTYPE html>
<html>
<head>
<?php
        $page_name = "Prescription";
        include_once("inc/head.php");
        if(!isset($_SESSION['tp_app']) && $_SESSION['tp_app'] == ""){
            header("location:index.php");
            exit();
        }
        
        $selectpackunit = mysqli_query($link,"select * from tp_prescription ORDER BY tp_p_id DESC");
        $nmselectpackunit = mysqli_num_rows($selectpackunit);
?>
</head>
<body>
      <?php include_once("inc/top-pan.php"); ?>
      <main id="main" class="main">
          <div class="pagetitle">
              <h1>Prescription</h1>
              <nav>
                  <ol class="breadcrumb">
                      <li class="breadcrumb-item"><a href="<?php echo SITE_ROOT_ADMIN;?>dashboard.php">Dashboard</a></li>
                      <li class="breadcrumb-item">Prescription</li>
                  </ol>
              </nav>
          </div>
          <section class="section">
              <?php if(isset($_SESSION["msg"])){echo '<div class="container-fluid">'.$_SESSION["msg"].'</div>';}unset($_SESSION["msg"]); ?>
              <div class="row"><div class="col-lg-12"><div class="float-end mb-3"><a class="btn btn-primary" href="prescription_add.php">Add Prescription</a></div></div></div>
              <div class="row">
                  <div class="col-lg-12">
                      <div class="card">
                          <div class="card-body">
                              <h5 class="card-title">Prescription</h5>
                              <table class="table datatable">
                                  <thead>
                                      <tr>
                                          <th>ID</th>
                                          <th>Name</th>
                                          <th>Action</th>
                                      </tr>
                                  </thead>
                                  <tbody>
                                      <?php if($nmselectpackunit > 0){ 
                                              while($rwselectpackunit = mysqli_fetch_array($selectpackunit)){
                                      ?>
                                                <tr>
                                                  <td><?php echo $rwselectpackunit['tp_p_id']; ?></td>
                                                  <td><?php echo $rwselectpackunit['tp_prescription_title']; ?></td>
                                                  <td>
                                                    <a class="btn btn-sm btn-primary" href="prescription_add.php?id=<?php echo $rwselectpackunit['tp_p_id']; ?>"><i class="bi bi-eject"></i></a>
                                                    <a class="btn btn-sm btn-success copy_prescription" href="javascript:void(0)" data-id="<?php echo $rwselectpackunit['tp_p_id']; ?>"><i class="bi bi-files"></i></a>
                                                    <a class="btn btn-sm btn-danger delete_prescription" href="javascript:void(0)"  data-id="<?php echo $rwselectpackunit['tp_p_id']; ?>"><i class="bi bi-trash" ></i></a>
                                                  </td>
                                                </tr>
                                      <?php    }
                                            }else{ ?>
                                              <tr>
                                                <td class="text-danger text-center" colspan="3">No Prescription Found.</td>
                                              </tr>
                                      <?php } ?>
                                  </tbody>
                              </table>
                          </div>
                      </div>
                  </div>
              </div>
          </section>
      </main>
      <?php include_once("inc/footer.php"); ?>
      <?php include_once("js.php"); ?>
      <!-- Copy Prescription Modal -->
      <div class="modal fade" id="copyPrescriptionModal" tabindex="-1" aria-labelledby="copyPrescriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="copyPrescriptionModalLabel">Copy Prescription</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form id="copyPrescriptionForm">
                <input type="hidden" id="copy_prescription_id" name="id" />
                <div class="mb-3">
                  <label for="new_prescription_name" class="form-label">New Prescription Name</label>
                  <input type="text" class="form-control" id="new_prescription_name" name="new_name" required />
                </div>
                <div class="mb-3">
                  <label for="new_excel_id" class="form-label">Excel ID</label>
                  <input type="text" class="form-control" id="new_excel_id" name="excel_id" required />
                </div>
                <div class="mb-3">
                  <label for="new_sheet_id" class="form-label">Sheet ID</label>
                  <input type="text" class="form-control" id="new_sheet_id" name="sheet_id" required />
                </div>
                <div id="copy_prescription_error" class="text-danger mb-2" style="display:none;"></div>
                <button type="submit" class="btn btn-primary">Copy</button>
              </form>
            </div>
          </div>
        </div>
      </div>
      <script>
          jQuery(document).ready(function(){
              jQuery(document).on('click','.delete_prescription',function(){
                  var dataid = jQuery(this).data("id");
                  if (confirm("Are you sure you want to delete this prescription?") == true) {
                      $.ajax({
                        type:'post',
                        url:base_url+'prescription_db.php',
                        data:"action=delete&id="+dataid,
                        success:function(url){
                          window.location.href = url;
                          return false;
                        },
                      });
                  }
              });
              // Copy prescription
              jQuery(document).on('click','.copy_prescription',function(){
                  var dataid = jQuery(this).data("id");
                  jQuery('#copy_prescription_id').val(dataid);
                  jQuery('#new_prescription_name').val('');
                  jQuery('#new_excel_id').val('');
                  jQuery('#new_sheet_id').val('');
                  jQuery('#copy_prescription_error').hide();
                  // Fetch original Excel ID and Sheet ID
                  jQuery.ajax({
                      type: 'post',
                      url: base_url+'prescription_db.php',
                      data: {action: 'get_excel_sheet', id: dataid},
                      dataType: 'json',
                      success: function(resp){
                          if(resp && resp.excel_id){
                              jQuery('#new_excel_id').val(resp.excel_id);
                          }
                          if(resp && resp.sheet_id){
                              jQuery('#new_sheet_id').val(''); // Always empty for clone
                          }
                      }
                  });
                  var modal = new bootstrap.Modal(document.getElementById('copyPrescriptionModal'));
                  modal.show();
              });
              jQuery('#copyPrescriptionForm').on('submit', function(e){
                  e.preventDefault();
                  var id = jQuery('#copy_prescription_id').val();
                  var new_name = jQuery('#new_prescription_name').val().trim();
                  var excel_id = jQuery('#new_excel_id').val().trim();
                  var sheet_id = jQuery('#new_sheet_id').val().trim();
                  if(new_name == '' || excel_id == '' || sheet_id == ''){
                      jQuery('#copy_prescription_error').text('All fields are required.').show();
                      return false;
                  }
                  jQuery.ajax({
                      type: 'post',
                      url: base_url+'prescription_db.php',
                      data: {action: 'clone', id: id, new_name: new_name, excel_id: excel_id, sheet_id: sheet_id},
                      success: function(resp){
                          if(resp === 'success'){
                              window.location.href = 'prescription.php';
                          }else{
                              jQuery('#copy_prescription_error').text(resp).show();
                          }
                      },
                      error: function(){
                          jQuery('#copy_prescription_error').text('An error occurred.').show();
                      }
                  });
              });
          });
      </script>
</body>
</html>