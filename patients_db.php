<?php include_once 'inc/connection.php';
      include_once 'inc/functions.php';
      $action = $_POST['action'];
      if($action == 'add'){
          $errors = "";
          if(!(isset($_POST["tp_p_mobile_no"]) && trim($_POST["tp_p_mobile_no"] != ""))){
              $errors .= "Mobile Number Is Required.<br/>";
          }
          if(!(isset($_POST["tp_p_whatsapp_no"]) && trim($_POST["tp_p_whatsapp_no"] != ""))){
              $errors .= "WhatsApp Number Is Required.<br/>";
          }

          $selectunitname = mysqli_query($link,"select * from tp_patients where tp_p_mobile_no ='".$_POST["tp_p_mobile_no"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Mobile Number Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }
          
          $tp_p_middlename = isset($_POST['tp_p_middlename']) ? mysqli_real_escape_string($link, $_POST['tp_p_middlename']) : '';
          $tp_p_whatsapp_no = isset($_POST['tp_p_whatsapp_no']) && trim($_POST['tp_p_whatsapp_no']) != '' ? mysqli_real_escape_string($link, $_POST['tp_p_whatsapp_no']) : mysqli_real_escape_string($link, $_POST['tp_p_mobile_no']);

          $update_qry = "INSERT INTO `tp_patients`(`tp_p_firstname`, `tp_p_middlename`, `tp_p_lastname`, `tp_p_mobile_no`, `tp_p_whatsapp_no`) VALUES ('".mysqli_real_escape_string($link, $_POST['tp_p_firstname'])."','".$tp_p_middlename."','".mysqli_real_escape_string($link, $_POST['tp_p_lastname'])."','".mysqli_real_escape_string($link, $_POST['tp_p_mobile_no'])."','".$tp_p_whatsapp_no."')";
          $run_upd = mysqli_query($link,$update_qry); 
          $last_id = mysqli_insert_id($link);
          if(isset($_POST['tp_p_query_type'])){
              for($m=0;$m<count($_POST['tp_p_query_type']);$m++){
                   $selectassign = mysqli_query($link,"select * from tp_patients_assign where tp_p_patient_id='".$last_id."' and tp_p_q_type_id='".$_POST['tp_p_query_type_id'][$m]."'");
                   if(mysqli_num_rows($selectassign)<=0){
                       mysqli_query($link,"insert into tp_patients_assign (tp_p_patient_id,tp_p_q_type_id,tp_p_q_user_id) values ('".$last_id."','".$_POST['tp_p_query_type_id'][$m]."','".$_POST['tp_p_query_type'][$m]."')");
                   }else{
                       mysqli_query($link,"update tp_patients_assign set tp_p_q_user_id='".$_POST['tp_p_query_type'][$m]."' where tp_p_patient_id='".$last_id."' and tp_p_q_type_id='".$_POST['tp_p_query_type_id'][$m]."'");
                   }
                   
              }
          }
          
          $_SESSION["msg"] = "<div class='alert alert-success'>Patient ".ucfirst($_POST['tp_p_firstname'])." Inserted Successfully.</div>";
          header("location:patients.php");
          exit();

      }else if($action == 'edit'){
          $errors = "";

          if(!(isset($_POST["tp_p_mobile_no"]) && trim($_POST["tp_p_mobile_no"] != ""))){
              $errors .= "Mobile Number Is Required.<br/>";
          }
          if(!(isset($_POST["tp_p_whatsapp_no"]) && trim($_POST["tp_p_whatsapp_no"] != ""))){
              $errors .= "WhatsApp Number Is Required.<br/>";
          }

          $selectunitname = mysqli_query($link,"select * from tp_patients where tp_p_mobile_no='".$_POST["tp_p_mobile_no"]."' and tp_p_id!='".$_POST["id"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Mobile Number Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }
          
          $tp_p_middlename = isset($_POST['tp_p_middlename']) ? mysqli_real_escape_string($link, $_POST['tp_p_middlename']) : '';
          $tp_p_whatsapp_no = isset($_POST['tp_p_whatsapp_no']) && trim($_POST['tp_p_whatsapp_no']) != '' ? mysqli_real_escape_string($link, $_POST['tp_p_whatsapp_no']) : mysqli_real_escape_string($link, $_POST['tp_p_mobile_no']);

          $update_qry = "UPDATE `tp_patients` SET `tp_p_firstname`='".mysqli_real_escape_string($link, $_POST["tp_p_firstname"])."', `tp_p_middlename`='".$tp_p_middlename."', `tp_p_lastname`='".mysqli_real_escape_string($link, $_POST["tp_p_lastname"])."', `tp_p_mobile_no`='".mysqli_real_escape_string($link, $_POST["tp_p_mobile_no"])."', `tp_p_whatsapp_no`='".$tp_p_whatsapp_no."' WHERE `tp_p_id`='".mysqli_real_escape_string($link, $_POST["id"])."'";

          $run_upd = mysqli_query($link,$update_qry); 
          
          if(isset($_POST['tp_p_query_type'])){
              for($m=0;$m<count($_POST['tp_p_query_type']);$m++){
                   $selectassign = mysqli_query($link,"select * from tp_patients_assign where tp_p_patient_id='".$_POST["id"]."' and tp_p_q_type_id='".$_POST['tp_p_query_type_id'][$m]."'");
                   if(mysqli_num_rows($selectassign)<=0){
                       mysqli_query($link,"insert into tp_patients_assign (tp_p_patient_id,tp_p_q_type_id,tp_p_q_user_id) values ('".$_POST["id"]."','".$_POST['tp_p_query_type_id'][$m]."','".$_POST['tp_p_query_type'][$m]."')");
                   }else{
                       mysqli_query($link,"update tp_patients_assign set tp_p_q_user_id='".$_POST['tp_p_query_type'][$m]."' where tp_p_patient_id='".$_POST["id"]."' and tp_p_q_type_id='".$_POST['tp_p_query_type_id'][$m]."'");
                   }
                   
              }
          }
          $_SESSION["msg"] = "<div class='alert alert-success'>Patient ".ucfirst($_POST["tp_p_firstname"])." Updated Successfully.</div>";
          header("location:patient_add.php?id=".$_POST["id"]);
          exit();

      }else if($action == 'delete'){ 
          $update_qry = "delete from tp_patients where tp_p_id='".$_POST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Patient Deleted Successfully.</div>";
          echo "patients.php";
      }else{
          $_SESSION["msg"] = "<div class='alert alert-danger'>Action not found.</div>";
          header("location:patients.php");
          exit();
      }
?>