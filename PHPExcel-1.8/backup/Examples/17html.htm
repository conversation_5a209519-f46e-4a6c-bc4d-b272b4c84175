<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<!-- Generated by PHPExcel - http://www.phpexcel.net -->
<html>
  <head>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <title>Office 2007 XLSX Test Document</title>
      <meta name="author" content="Maarten Balliauw" />
      <meta name="title" content="Office 2007 XLSX Test Document" />
      <meta name="description" content="Test document for Office 2007 XLSX, generated using PHP classes." />
      <meta name="subject" content="Office 2007 XLSX Test Document" />
      <meta name="keywords" content="office 2007 openxml php" />
      <meta name="category" content="Test result file" />
      <meta name="company" content="Microsoft Corporation" />
    <style type="text/css">
      html { font-family:Calibri, Arial, Helvetica, sans-serif; font-size:11pt; background-color:white }
      table { border-collapse:collapse; page-break-after:always }
      .gridlines td { border:1px dotted black }
      .gridlines th { border:1px dotted black }
      .b { text-align:center }
      .e { text-align:center }
      .f { text-align:right }
      .inlineStr { text-align:left }
      .n { text-align:right }
      .s { text-align:left }
      td.style0 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style0 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style1 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style1 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style2 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; font-weight:bold; text-decoration:underline; color:#000000; font-family:'Candara'; font-size:20pt; background-color:white }
      th.style2 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; font-weight:bold; text-decoration:underline; color:#000000; font-family:'Candara'; font-size:20pt; background-color:white }
      td.style3 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style3 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style4 { vertical-align:middle; text-align:justify; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style4 { vertical-align:middle; text-align:justify; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style5 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style5 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style6 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style6 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style7 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style7 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style8 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style8 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:1px solid #000000 !important; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style9 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style9 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style10 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style10 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style11 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style11 { vertical-align:bottom; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style12 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style12 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style13 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style13 { vertical-align:bottom; border-bottom:1px solid #000000 !important; border-top:none #000000; border-left:none #000000; border-right:1px solid #000000 !important; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style14 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:3px solid #993300 !important; border-top:3px solid #993300 !important; border-left:3px solid #993300 !important; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style14 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:3px solid #993300 !important; border-top:3px solid #993300 !important; border-left:3px solid #993300 !important; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style15 { vertical-align:bottom; border-bottom:3px solid #993300 !important; border-top:3px solid #993300 !important; border-left:none #000000; border-right:3px solid #993300 !important; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      th.style15 { vertical-align:bottom; border-bottom:3px solid #993300 !important; border-top:3px solid #993300 !important; border-left:none #000000; border-right:3px solid #993300 !important; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:white }
      td.style16 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      th.style16 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      td.style17 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#FFFFFF; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      th.style17 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#FFFFFF; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      td.style18 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#FFFFFF; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      th.style18 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#FFFFFF; font-family:'Calibri'; font-size:11pt; background-color:#808080 }
      td.style19 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      th.style19 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      td.style20 { vertical-align:bottom; text-align:left; padding-left:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:1px solid #000000 !important; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      th.style20 { vertical-align:bottom; text-align:left; padding-left:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:1px solid #000000 !important; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      td.style21 { vertical-align:bottom; text-align:left; padding-left:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      th.style21 { vertical-align:bottom; text-align:left; padding-left:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:none #000000; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      td.style22 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:1px solid #000000 !important; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      th.style22 { vertical-align:bottom; text-align:right; padding-right:0px; border-bottom:none #000000; border-top:1px solid #000000 !important; border-left:none #000000; border-right:1px solid #000000 !important; font-weight:bold; color:#000000; font-family:'Calibri'; font-size:11pt; background-color:#A0A0A0 }
      td.style23 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; font-weight:bold; text-decoration:underline; color:#FFFFFF; font-family:'Candara'; font-size:20pt; background-color:#808080 }
      th.style23 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; font-weight:bold; text-decoration:underline; color:#FFFFFF; font-family:'Candara'; font-size:20pt; background-color:#808080 }
      td.style24 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:8pt; background-color:white }
      th.style24 { vertical-align:bottom; border-bottom:none #000000; border-top:none #000000; border-left:none #000000; border-right:none #000000; color:#000000; font-family:'Calibri'; font-size:8pt; background-color:white }
      table.sheet0 col.col0 { width:42pt }
      table.sheet0 col.col1 { width:111.15555428pt }
      table.sheet0 col.col2 { width:42pt }
      table.sheet0 col.col3 { width:56.93333268pt }
      table.sheet0 col.col4 { width:56.93333268pt }
      table.sheet0 tr { height:15pt }
    </style>
  </head>

  <body>
<style>
@page { margin-left: 0.7in; margin-right: 0.7in; margin-top: 0.75in; margin-bottom: 0.75in; }
body { margin-left: 0.7in; margin-right: 0.7in; margin-top: 0.75in; margin-bottom: 0.75in; }
</style>
    <table border="0" cellpadding="0" cellspacing="0" id="sheet0" class="sheet0 gridlines">
        <col class="col0">
        <col class="col1">
        <col class="col2">
        <col class="col3">
        <col class="col4">
        <tbody>
          <tr class="row0">
            <td class="column0 style16 null">
<div style="position: relative;"><img style="position: absolute; z-index: 1; left: 0px; top: 0px; width: 33px; height: 36px;" src="./images/officelogo.jpg" border="0" /></div></td>
            <td class="column1 style23 s">Invoice</td>
            <td class="column2 style16 null"></td>
            <td class="column3 style17 n">17-May-25</td>
            <td class="column4 style18 s">#12566</td>
          </tr>
          <tr class="row1">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
          <tr class="row2">
            <td class="column0 style20 s">Product Id</td>
            <td class="column1 style21 s">Description</td>
            <td class="column2 style19 s">Price</td>
            <td class="column3 style19 s">Amount</td>
            <td class="column4 style22 s">Total</td>
          </tr>
          <tr class="row3">
            <td class="column0 style6 n">1001</td>
            <td class="column1 style9 s">PHP for dummies</td>
            <td class="column2 style9 n">20</td>
            <td class="column3 style9 n">1</td>
            <td class="column4 style11 f">EUR 20.00 </td>
          </tr>
          <tr class="row4">
            <td class="column0 style7 n">1012</td>
            <td class="column1 style5 s">OpenXML for dummies</td>
            <td class="column2 style0 n">22</td>
            <td class="column3 style0 n">2</td>
            <td class="column4 style12 f">EUR 44.00 </td>
          </tr>
          <tr class="row5">
            <td class="column0 style7 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style12 f"></td>
          </tr>
          <tr class="row6">
            <td class="column0 style7 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style12 f"></td>
          </tr>
          <tr class="row7">
            <td class="column0 style7 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style12 f"></td>
          </tr>
          <tr class="row8">
            <td class="column0 style7 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style12 f"></td>
          </tr>
          <tr class="row9">
            <td class="column0 style8 null"></td>
            <td class="column1 style10 null"></td>
            <td class="column2 style10 null"></td>
            <td class="column3 style10 null"></td>
            <td class="column4 style13 null"></td>
          </tr>
          <tr class="row10">
            <td class="column0 style0 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style3 s">Total excl.:</td>
            <td class="column4 style1 f">EUR 64.00 </td>
          </tr>
          <tr class="row11">
            <td class="column0 style0 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style3 s">VAT:</td>
            <td class="column4 style1 f">EUR 13.44 </td>
          </tr>
          <tr class="row12">
            <td class="column0 style0 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style14 s">Total incl.:</td>
            <td class="column4 style15 f">EUR 77.44 </td>
          </tr>
          <tr class="row13">
            <td class="column0 style0 null"></td>
            <td class="column1 style0 null"></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style0 s"></td>
          </tr>
          <tr class="row14">
            <td class="column0 style0 null"></td>
            <td class="column1 style0 null">
<div style="position: relative;"><img style="position: absolute; z-index: 1; left: 110px; top: 0px; width: 152px; height: 42px;" src="./images/paid.png" border="0" /></div></td>
            <td class="column2 style0 null"></td>
            <td class="column3 style0 null"></td>
            <td class="column4 style0 s"></td>
          </tr>
          <tr class="row15">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
          <tr class="row16">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
          <tr class="row17">
            <td class="column0 style4 inlineStr style0" colspan="5">This invoice is <span style="font-weight:bold; font-style:italic; color:#008000; font-family:'Calibri'; font-size:11pt">payable within thirty days after the end of the month</span>, unless specified otherwise on the invoice.</td>
          </tr>
          <tr class="row22">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
          <tr class="row23">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3 style0 null">
<div style="position: relative;"><img style="position: absolute; z-index: 1; left: 10px; top: 0px; width: 157px; height: 36px;" src="./images/phpexcel_logo.gif" border="0" /></div></td>
            <td class="column4 style0 null"></td>
          </tr>
          <tr class="row24">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
          <tr class="row25">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4 style3 s"><a href="http://www.phpexcel.net" title="Navigate to website">www.phpexcel.net</a></td>
          </tr>
          <tr class="row26">
            <td class="column0">&nbsp;</td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4 style3 s">Terms and conditions</td>
          </tr>
          <tr class="row27">
            <td class="column0 style0 null"></td>
            <td class="column1">&nbsp;</td>
            <td class="column2">&nbsp;</td>
            <td class="column3">&nbsp;</td>
            <td class="column4">&nbsp;</td>
          </tr>
<tr><td></td></tr>        </tbody>
    </table>
  </body>
</html>
