<?php include_once 'inc/connection.php';
      include_once 'inc/functions.php';
      $action = $_POST['action'];
      if($action == 'add'){
          $errors = "";
          if(!(isset($_POST["tp_qt_name"]) && trim($_POST["tp_qt_name"] != ""))){
              $errors .= "Title Is Required.<br/>";
          }

          $selectunitname2 = mysqli_query($link,"select * from tp_querytype where tp_qt_name ='".$_POST["tp_qt_name"]."'");
          if(mysqli_num_rows($selectunitname2)>0){
              $errors .= "Title Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }

          $update_qry = "INSERT INTO `tp_querytype`(`tp_qt_name`,`tp_qt_default_user`) VALUES ('".mysqli_real_escape_string($link,$_POST['tp_qt_name'])."','".$_POST['tp_qt_default_user']."')";  
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Query Type ".ucfirst(mysqli_real_escape_string($link,$_POST['tp_qt_name']))." Inserted Successfully.</div>";
          header("location:querytype.php");
          exit();

      }else if($action == 'edit'){
          $errors = "";

          if(!(isset($_POST["tp_qt_name"]) && trim($_POST["tp_qt_name"] != ""))){
              $errors .= "Title Is Required.<br/>";
          }

          $selectunitname = mysqli_query($link,"select * from tp_querytype where tp_qt_name ='".$_POST["tp_qt_name"]."' and tp_qt_id!='".$_POST["id"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Title Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }

          $update_qry = "UPDATE `tp_querytype` SET `tp_qt_name`='".mysqli_real_escape_string($link,$_POST["tp_qt_name"])."',`tp_qt_default_user`='".$_POST['tp_qt_default_user']."' WHERE `tp_qt_id`='".$_POST["id"]."'";

          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Query Type ".ucfirst(mysqli_real_escape_string($link,$_POST['tp_qt_name']))." Updated Successfully.</div>";
          header("location:querytype_add.php?id=".$_POST["id"]);
          exit();

      }else if($action == 'delete'){ 
          $update_qry = "delete from tp_querytype where tp_qt_id='".$_POST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Query Type Deleted Successfully.</div>";
          echo "querytype.php";
      }else{
          $_SESSION["msg"] = "<div class='alert alert-danger'>Action not found.</div>";
          header("location:querytype.php");
          exit();
      }
?>