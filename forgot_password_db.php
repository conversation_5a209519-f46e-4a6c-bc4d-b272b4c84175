<?php   require_once("../inc/connection.php");

		$unm = $_REQUEST["unm"];
		$myaction = $_REQUEST["myaction"];

		function generatepass($length = 10) {
			$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
			$randomString = '';
			for ($i = 0; $i < $length; $i++) {
				$randomString .= $characters[rand(0, strlen($characters) - 1)];
			}
			return $randomString;
		}

		
		$password = generatepass(6);
		$selectuser1 = mysqli_query($link,"select * from tp_admin where tp_username='".$unm."'");
		if(mysqli_num_rows($selectuser1)>0){
			$rwselectuser1 = mysqli_fetch_array($selectuser1);
			$to = $rwselectuser1['tp_email_address'];
			$subject = "Forgot Password";
			$message = "<html>
							<head>
								<title>Forgot Pasword</title>
							</head>
							<body>
								<p>Dear Admin</p>
								<p>Your Password has been successfully changed in Patient App and the new Password is ".$password."</p>
								<p> Kind regards,<br/><br/>Patient App</p>
							</body>
						</html>";
			$headers = "MIME-Version: 1.0" . "\r\n";
			$headers .= "Content-type:text/html;charset=iso-8859-1" . "\r\n";
			$headers .= 'From: <Patient App>' . "\r\n";
			mail($to,$subject,$message,$headers);
			echo "0";
			exit;
		}else{
			echo "1";
			exit;
		}
		
?>