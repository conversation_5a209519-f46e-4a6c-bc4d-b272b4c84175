<?php include_once 'inc/connection.php';
      include_once 'inc/functions.php';
      $action = $_REQUEST['action'];
      if($action == 'add'){
          $errors = "";
          if(!(isset($_POST["tp_prescription_title"]) && trim($_POST["tp_prescription_title"] != ""))){
              $errors .= "Name Is Required.<br/>";
          }

          if(trim($_POST['tp_excel_id']) != '' && trim($_POST['tp_sheet_id']) != ''){

                // Unique Excel ID + Sheet ID
                $check_combo = mysqli_query($link, "SELECT * FROM tp_prescription WHERE tp_excel_id='".mysqli_real_escape_string($link, $_POST['tp_excel_id'])."' AND tp_sheet_id='".mysqli_real_escape_string($link, $_POST['tp_sheet_id'])."'");
                if(mysqli_num_rows($check_combo) > 0){
                    $errors .= "This Excel ID and Sheet ID combination already exists.<br/>";
                }

          }
          
          $selectunitname = mysqli_query($link,"select * from tp_prescription where tp_prescription_title ='".$_POST["tp_prescription_title"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Name Already Exists.<br/>";
          }
          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }
          
          
          $title = $_POST['tp_prescription_title'];

          // Process multiple questions data and auto-create child nodes
          $processed_data = processMultipleQuestionsAndCreateNodes($_POST);

          $finaldata1 = serialize($processed_data['node']);
          $finaldata2 = serialize($processed_data['nodeoption']);
          $finaldata3 = serialize($processed_data['nodeend']);
          $finaldata4 = serialize($processed_data['nodeendoption']);
          $finaldata5 = serialize($processed_data['nodemainaa']);
          $finaldata6 = serialize($processed_data['nodemainaa2']);
          $finaldata7 = serialize($processed_data['nodegender']);
          $finaldata8 = serialize($processed_data['mainnodegender']);
          $finaldata9 = serialize($processed_data['nodecolor']);
          $finaldata10 = serialize($processed_data['mainnodecolor']);
          $finaldata11 = serialize($processed_data['multiple_questions']);
          $finaldata = $finaldata1."@@@@@".$finaldata2."@@@@@".$finaldata3."@@@@@".$finaldata4."@@@@@".$finaldata5."@@@@@".$finaldata6."@@@@@".$finaldata7."@@@@@".$finaldata8."@@@@@".$finaldata9."@@@@@".$finaldata10."@@@@@".$finaldata11;
          $tp_p_parentnam = $_POST['nodemain'];
          $update_qry = "INSERT INTO `tp_prescription`(`tp_prescription_title`, `tp_p_parentname`, `tp_p_childnodename`, `tp_excel_id`, `tp_sheet_id`) VALUES ('".$title."','".$tp_p_parentnam."','".$finaldata."','".$_POST['tp_excel_id']."','".$_POST['tp_sheet_id']."')";  
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Prescription ".ucfirst($title)." Inserted Successfully.</div>";
          header("location:prescription.php");
          exit();

      }else if($action == 'edit'){
          $errors = "";

          if(!(isset($_POST["tp_prescription_title"]) && trim($_POST["tp_prescription_title"] != ""))){
              $errors .= "Name Is Required.<br/>";
          }
          
          // Unique Excel ID + Sheet ID (excluding current record)

          if(trim($_POST['tp_excel_id']) != '' && trim($_POST['tp_sheet_id']) != ''){
            $check_combo = mysqli_query($link, "SELECT * FROM tp_prescription WHERE tp_excel_id='".mysqli_real_escape_string($link, $_POST['tp_excel_id'])."' AND tp_sheet_id='".mysqli_real_escape_string($link, $_POST['tp_sheet_id'])."' AND tp_p_id!='".mysqli_real_escape_string($link, $_POST['id'])."'");
            if(mysqli_num_rows($check_combo) > 0){
                $errors .= "This Excel ID and Sheet ID combination already exists.<br/>";
            }
          }
          

          $selectunitname = mysqli_query($link,"select * from tp_prescription where tp_prescription_title='".$_POST["tp_prescription_title"]."' and tp_p_id!='".$_POST["id"]."'");
          if(mysqli_num_rows($selectunitname)>0){
              $errors .= "Name Already Exists.<br/>";
          }

          if(isset($errors) && $errors != ""){
              $finalmsg = rtrim($errors,"<br/>");
              $_SESSION["msg"] = "<div class='alert alert-danger'>".$finalmsg."</div>";
              header("location:".$_SERVER['HTTP_REFERER']);
              exit();
          }
          
          $title = $_POST['tp_prescription_title'];
          $finaldata1 = serialize($_POST['node']);
          $finaldata2 = serialize($_POST['nodeoption']);
          $finaldata3 = serialize($_POST['nodeend']);
          $finaldata4 = serialize($_POST['nodeendoption']);
          $finaldata5 = serialize($_POST['nodemainaa']);
          $finaldata6 = serialize($_POST['nodemainaa2']);
          $finaldata7 = serialize($_POST['nodegender']);
          $finaldata8 = serialize($_POST['mainnodegender']);
          $finaldata9 = serialize($_POST['nodecolor']);
          $finaldata10 = serialize($_POST['mainnodecolor']);
          $finaldata = $finaldata1."@@@@@".$finaldata2."@@@@@".$finaldata3."@@@@@".$finaldata4."@@@@@".$finaldata5."@@@@@".$finaldata6."@@@@@".$finaldata7."@@@@@".$finaldata8."@@@@@".$finaldata9."@@@@@".$finaldata10;
          $tp_p_parentnam = $_POST['nodemain'];
          
          $update_qry = "UPDATE `tp_prescription` SET `tp_prescription_title`='".$title."',`tp_p_parentname`='".$tp_p_parentnam."',`tp_p_childnodename`='".$finaldata."',`tp_excel_id`='".$_POST['tp_excel_id']."',`tp_sheet_id`='".$_POST['tp_sheet_id']."' WHERE `tp_p_id`='".$_REQUEST['id']."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Prescription ".ucfirst($title)." Updated Successfully.</div>";
          header("location:prescription_add.php?id=".$_REQUEST['id']);
          exit();

      }else if($action == 'delete'){ 
          $update_qry = "delete from tp_prescription where tp_p_id='".$_REQUEST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $_SESSION["msg"] = "<div class='alert alert-success'>Prescription Deleted Successfully.</div>";
          echo "prescription.php";
      }else if($action == 'clone'){
          $id = $_REQUEST['id'];
          $new_name = trim($_REQUEST['new_name']);
          $excel_id = trim($_REQUEST['excel_id']);
          $sheet_id = trim($_REQUEST['sheet_id']);
          if($new_name == '' || $excel_id == '' || $sheet_id == ''){
              echo 'All fields are required.';
              exit();
          }
          $check = mysqli_query($link, "SELECT * FROM tp_prescription WHERE tp_prescription_title='".mysqli_real_escape_string($link, $new_name)."'");
          if(mysqli_num_rows($check) > 0){
              echo 'Name already exists.';
              exit();
          }

          if(trim($_POST['tp_excel_id']) != '' && trim($_POST['tp_sheet_id']) != ''){

                // Check unique Excel ID + Sheet ID
                $check_combo = mysqli_query($link, "SELECT * FROM tp_prescription WHERE tp_excel_id='".mysqli_real_escape_string($link, $excel_id)."' AND tp_sheet_id='".mysqli_real_escape_string($link, $sheet_id)."'");
                if(mysqli_num_rows($check_combo) > 0){
                    echo 'This Excel ID and Sheet ID combination already exists.';
                    exit();
                }

          }
          
          $orig = mysqli_query($link, "SELECT * FROM tp_prescription WHERE tp_p_id='".mysqli_real_escape_string($link, $id)."'");
          if(mysqli_num_rows($orig) == 0){
              echo 'Original prescription not found.';
              exit();
          }
          $row = mysqli_fetch_assoc($orig);
          $tp_p_parentname = $row['tp_p_parentname'];
          $tp_p_childnodename = $row['tp_p_childnodename'];
          $insert = mysqli_query($link, "INSERT INTO tp_prescription (tp_prescription_title, tp_p_parentname, tp_p_childnodename, tp_excel_id, tp_sheet_id) VALUES ('".mysqli_real_escape_string($link, $new_name)."', '".mysqli_real_escape_string($link, $tp_p_parentname)."', '".mysqli_real_escape_string($link, $tp_p_childnodename)."', '".mysqli_real_escape_string($link, $excel_id)."', '".mysqli_real_escape_string($link, $sheet_id)."')");
          if($insert){
              $_SESSION["msg"] = "<div class='alert alert-success'>Prescription cloned successfully.</div>";
              echo 'success';
          }else{
              echo 'Failed to clone prescription.';
          }
          exit();
      }else if($action == 'get_excel_sheet'){
          $id = $_REQUEST['id'];
          $orig = mysqli_query($link, "SELECT tp_excel_id, tp_sheet_id FROM tp_prescription WHERE tp_p_id='".mysqli_real_escape_string($link, $id)."'");
          if(mysqli_num_rows($orig) == 0){
              echo json_encode(['excel_id'=>'','sheet_id'=>'']);
              exit();
          }
          $row = mysqli_fetch_assoc($orig);
          echo json_encode(['excel_id'=>$row['tp_excel_id'],'sheet_id'=>$row['tp_sheet_id']]);
          exit();
      }else if($action == 'get_data'){ 
          $update_qry = "select * from tp_prescription where tp_p_id='".$_REQUEST["id"]."'";
          $run_upd = mysqli_query($link,$update_qry); 
          $run_rupd = mysqli_fetch_array($run_upd); 
          echo $run_rupd['tp_p_childnodename'];
      }else{
          $_SESSION["msg"] = "<div class='alert alert-danger'>Action not found.</div>";
          header("location:prescription.php");
          exit();
      }
?>