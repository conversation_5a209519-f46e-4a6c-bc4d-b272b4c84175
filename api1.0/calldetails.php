<?php   include_once '../inc/connection.php';
        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://kommuno.com/v1/kcrm/10001461/getCallsDetails',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS =>'{
         "callDirection":"",
         "startDate":"'.date("Y-m-d").'",
         "endDate":"'.date("Y-m-d").'",
         "batchSize":60
        }',
          CURLOPT_HTTPHEADER => array(
            'accesstoken: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IjEwMDAxNDYxIiwiUk9MRSI6IkNsaWVudCIsInVzZXJfcm9sZV9pZCI6MjczMSwiaWF0IjoxNzUyMjI3NjU3LCJleHAiOjE3NTIyNjAwNTd9.tkIA4PCUN4gSgugBtbeT0Q5NismI03fmoTV_hN6S3zY',
            'accesskey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6IjEwMDAxNDYxIiwidXNlcl90b2tlbiI6ImV5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUoxYzJWeWJtRnRaU0k2SWpFd01EQXhORFl4SWl3aVVrOU1SU0k2SWtOc2FXVnVkQ0lzSW5WelpYSmZjbTlzWlY5cFpDSTZNamN6TVN3aWFXRjBJam94TnpVeU1qSTNOalUzTENKbGVIQWlPakUzTlRJeU5qQXdOVGQ5LnRrSUE0UENVTjRnU2d1Z0J0YmVUMFE1TmlzbUkwM2Ztb1RWX2hONlMzelkiLCJpYXQiOjE3NTIyMjgxNTYsImV4cCI6MjYxNjE0MTc1Nn0.mHlDU4Vb2gxo3o--zsnaASmClUUQdfESNogiReixsyM',
            'Content-Type: application/json'
          ),
        ));
        
        $response = curl_exec($curl);
        
        curl_close($curl);
        $datastring = json_decode($response);
        if($datastring->status == "200"){
            for($i=0;$i<count($datastring->data);$i++){
                $tc_cd_sme_id = $datastring->data[$i]->call_details->sme_id;
                $tc_cd_call_direction = $datastring->data[$i]->call_details->call_direction;
                $tc_cd_session_id = $datastring->data[$i]->call_details->session_id;
                $tc_cd_duration = $datastring->data[$i]->call_details->duration;
                $tc_cd_longcode = $datastring->data[$i]->call_details->longcode;
                $tc_cd_customer_number = $datastring->data[$i]->call_details->customer_number;
                $tc_cd_agent_number = $datastring->data[$i]->call_details->agent_number;
                $tc_cd_overall_call_status = $datastring->data[$i]->call_details->overall_call_status;
                $tc_cd_call_recording_status = $datastring->data[$i]->call_details->call_recording_status;
                $tc_cd_disconnected_by = $datastring->data[$i]->call_details->disconnected_by;
                $tc_cd_customer_name = $datastring->data[$i]->call_details->customer_name;
                $tc_cd_remarks = $datastring->data[$i]->call_details->remarks;
                $tc_cd_connected_duration = $datastring->data[$i]->call_details->connected_duration;
                $tc_cd_ringing_duration = $datastring->data[$i]->call_details->ringing_duration;
                $tc_cd_ivr_duration = $datastring->data[$i]->call_details->ivr_duration;
                $tc_cd_dtmf = $datastring->data[$i]->call_details->dtmf;
                $tc_cd_flow_name = $datastring->data[$i]->call_details->flow_name;
                $tc_cd_queue_name = $datastring->data[$i]->call_details->queue_name;
                $tc_cd_start_date_time = $datastring->data[$i]->call_details->start_date_time;
                $tc_cd_end_date_time = $datastring->data[$i]->call_details->end_date_time;
                $tc_cus_customer_number = $datastring->data[$i]->customer_details->customer_number;
                $tc_cus_customer_name = $datastring->data[$i]->customer_details->customer_name;
                $tc_cus_call_status = $datastring->data[$i]->customer_details->call_status;
                $tc_cus_duration = $datastring->data[$i]->customer_details->duration;
                $tc_cus_connected_duration = $datastring->data[$i]->customer_details->connected_duration;
                $tc_cus_ringing_duration = $datastring->data[$i]->customer_details->ringing_duration;
                if(isset($datastring->data[$i]->recording_details)){
                    $tc_rd_recording_path = $datastring->data[$i]->recording_details->recording_path;
                    $tc_rd_duration = $datastring->data[$i]->recording_details->duration;
                }
                
                $selectcall = mysqli_query($link,"select * from tp_calls where tc_cd_call_direction='".$tc_cd_call_direction."' and tc_cd_session_id='".$tc_cd_session_id."' and tc_cd_customer_number='".$tc_cd_customer_number."' and tc_cd_agent_number='".$tc_cd_agent_number."' and tc_cus_call_status='".$tc_cus_call_status."'");
                if(mysqli_num_rows($selectcall)<=0){
                    
                    mysqli_query($link,"INSERT INTO `tp_calls`(`tc_cd_sme_id`, `tc_cd_call_direction`, `tc_cd_session_id`, `tc_cd_duration`, `tc_cd_longcode`, `tc_cd_customer_number`, `tc_cd_agent_number`, `tc_cd_overall_call_status`, `tc_cd_call_recording_status`, `tc_cd_disconnected_by`, `tc_cd_customer_name`, `tc_cd_remarks`, `tc_cd_connected_duration`, `tc_cd_ringing_duration`, `tc_cd_ivr_duration`, `tc_cd_dtmf`, `tc_cd_flow_name`, `tc_cd_queue_name`, `tc_cd_start_date_time`, `tc_cd_end_date_time`, `tc_cus_customer_number`, `tc_cus_customer_name`, `tc_cus_call_status`, `tc_cus_duration`, `tc_cus_connected_duration`, `tc_cus_ringing_duration`, `tc_rd_recording_path`, `tc_rd_duration`) VALUES ('".$tc_cd_sme_id."','".$tc_cd_call_direction."','".$tc_cd_session_id."','".$tc_cd_duration."','".$tc_cd_longcode."','".$tc_cd_customer_number."','".$tc_cd_agent_number."','".$tc_cd_overall_call_status."','".$tc_cd_call_recording_status."','".$tc_cd_disconnected_by."','".$tc_cd_customer_name."','".$tc_cd_remarks."','".$tc_cd_connected_duration."','".$tc_cd_ringing_duration."','".$tc_cd_ivr_duration."','".$tc_cd_dtmf."','".$tc_cd_flow_name."','".$tc_cd_queue_name."','".$tc_cd_start_date_time."','".$tc_cd_end_date_time."','".$tc_cus_customer_number."','".$tc_cus_customer_name."','".$tc_cus_call_status."','".$tc_cus_duration."','".$tc_cus_connected_duration."','".$tc_cus_ringing_duration."','".$tc_rd_recording_path."','".$tc_rd_duration."')");
                    $last_id = mysqli_insert_id($link);
                    
                    if(isset($datastring->data[$i]->agent_details)){
                        for($j=0;$j<count($datastring->data[$i]->agent_details);$j++){
                            $tca_agent_id = $datastring->data[$i]->agent_details[$j]->agent_id;
                            $tca_agent_name = $datastring->data[$i]->agent_details[$j]->agent_name;
                            $tca_agent_email = $datastring->data[$i]->agent_details[$j]->agent_email;
                            $tca_agent_number = $datastring->data[$i]->agent_details[$j]->agent_number;
                            $tca_call_status = $datastring->data[$i]->agent_details[$j]->call_status;
                            $tca_call_route_reason = $datastring->data[$i]->agent_details[$j]->call_route_reason;
                            $tca_duration = $datastring->data[$i]->agent_details[$j]->duration;
                            $tca_connected_duration = $datastring->data[$i]->agent_details[$j]->connected_duration;
                            $tca_start_date_time = $datastring->data[$i]->agent_details[$j]->start_date_time;
                            $tca_end_date_time = $datastring->data[$i]->agent_details[$j]->end_date_time;
                            
                            mysqli_query($link,"INSERT INTO `tp_call_agents`(`tca_call_id`, `tca_agent_id`, `tca_agent_name`, `tca_agent_email`, `tca_agent_number`, `tca_call_status`, `tca_call_route_reason`, `tca_duration`, `tca_connected_duration`, `tca_start_date_time`, `tca_end_date_time`) VALUES ('".$last_id."','".$tca_agent_id."','".$tca_agent_name."','".$tca_agent_email."','".$tca_agent_number."','".$tca_call_status."','".$tca_call_route_reason."','".$tca_duration."','".$tca_connected_duration."','".$tca_start_date_time."','".$tca_end_date_time."')");
                        }
                    }
                    
                    if(isset($datastring->data[$i]->recording_details)){
                        $remote_file_url = $tc_rd_recording_path;
                        $local_file = $last_id.'.wav';
                        $copy = copy( $remote_file_url, "../UploadImages/recording/".$local_file );
                        mysqli_query($link,"update tp_calls set tc_rd_recording_local_path='".$local_file."' where tc_call_id='".$last_id."'");
                    }
                    
                }
                
            }
        }
?>