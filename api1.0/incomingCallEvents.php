<?php
// Set response header
header('Content-Type: application/json');

// Read raw POST data from the request body
$requestBody = file_get_contents('php://input');

// Get the full request URL
$requestUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") 
    . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

// Prepare response
$response = [
    'request_url' => $requestUrl,
];

// Check if the request body is not empty
if (!empty($requestBody)) {
    // Decode JSON if applicable
    $data = json_decode($requestBody, true);

    // Include data in response
    $response['status'] = 'success';
    $response['message'] = 'Request received successfully.';
    $response['data'] = $data;
} else {
    // Respond with an error message
    $response['status'] = 'error';
    $response['message'] = 'No data received.';
}

// Output the response as JSO<PERSON>
echo json_encode($response);
// Convert array to string using json_encode before logging
$log = json_encode($response, JSON_PRETTY_PRINT);

//Save string to log, use FILE_APPEND to append.
file_put_contents('./logs/incoming/log_incoming_'.date("j.n.Y").'.log', $log, FILE_APPEND);
?>
