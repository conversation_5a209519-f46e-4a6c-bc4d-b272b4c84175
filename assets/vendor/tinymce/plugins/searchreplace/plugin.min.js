!function(){function e(e){return e&&1==e.nodeType&&"false"===e.contentEditable}function t(t,n,r,i,o){function a(e,t){if(t=t||0,!e[0])throw"findAndReplaceDOMText cannot handle zero-length matches";var n=e.index;if(t>0){var r=e[t];if(!r)throw"Invalid capture group";n+=e[0].indexOf(r),e[0]=r}return[n,n+e[0].length,[e[0]]]}function s(t){var n;if(3===t.nodeType)return t.data;if(m[t.nodeName]&&!p[t.nodeName])return"";if(n="",e(t))return"\n";if((p[t.nodeName]||h[t.nodeName])&&(n+="\n"),t=t.firstChild)do n+=s(t);while(t=t.nextSibling);return n}function l(t,n,r){var i,o,a,s,l=[],c=0,u=t,d=n.shift(),f=0;e:for(;;){if((p[u.nodeName]||h[u.nodeName]||e(u))&&c++,3===u.nodeType&&(!o&&u.length+c>=d[1]?(o=u,s=d[1]-c):i&&l.push(u),!i&&u.length+c>d[0]&&(i=u,a=d[0]-c),c+=u.length),i&&o){if(u=r({startNode:i,startNodeIndex:a,endNode:o,endNodeIndex:s,innerNodes:l,match:d[2],matchIndex:f}),c-=o.length-s,i=null,o=null,l=[],d=n.shift(),f++,!d)break}else if(m[u.nodeName]&&!p[u.nodeName]||!u.firstChild){if(u.nextSibling){u=u.nextSibling;continue}}else if(!e(u)){u=u.firstChild;continue}for(;;){if(u.nextSibling){u=u.nextSibling;break}if(u.parentNode===t)break e;u=u.parentNode}}}function c(e){var t;if("function"!=typeof e){var n=e.nodeType?e:f.createElement(e);t=function(e,t){var r=n.cloneNode(!1);return r.setAttribute("data-mce-index",t),e&&r.appendChild(f.createTextNode(e)),r}}else t=e;return function(e){var n,r,i,o=e.startNode,a=e.endNode,s=e.matchIndex;if(o===a){var l=o;i=l.parentNode,e.startNodeIndex>0&&(n=f.createTextNode(l.data.substring(0,e.startNodeIndex)),i.insertBefore(n,l));var c=t(e.match[0],s);return i.insertBefore(c,l),e.endNodeIndex<l.length&&(r=f.createTextNode(l.data.substring(e.endNodeIndex)),i.insertBefore(r,l)),l.parentNode.removeChild(l),c}n=f.createTextNode(o.data.substring(0,e.startNodeIndex)),r=f.createTextNode(a.data.substring(e.endNodeIndex));for(var u=t(o.data.substring(e.startNodeIndex),s),d=[],p=0,m=e.innerNodes.length;p<m;++p){var h=e.innerNodes[p],g=t(h.data,s);h.parentNode.replaceChild(g,h),d.push(g)}var v=t(a.data.substring(0,e.endNodeIndex),s);return i=o.parentNode,i.insertBefore(n,o),i.insertBefore(u,o),i.removeChild(o),i=a.parentNode,i.insertBefore(v,a),i.insertBefore(r,a),i.removeChild(a),v}}var u,d,f,p,m,h,g=[],v=0;if(f=n.ownerDocument,p=o.getBlockElements(),m=o.getWhiteSpaceElements(),h=o.getShortEndedElements(),d=s(n)){if(t.global)for(;u=t.exec(d);)g.push(a(u,i));else u=d.match(t),g.push(a(u,i));return g.length&&(v=g.length,l(n,g,c(r))),v}}function n(e){function n(){function t(){o.statusbar.find("#next").disabled(!a(d+1).length),o.statusbar.find("#prev").disabled(!a(d-1).length)}function n(){e.windowManager.alert("Could not find the specified string.",function(){o.find("#find")[0].focus()})}var r,i={};r=tinymce.trim(e.selection.getContent({format:"text"}));var o=e.windowManager.open({layout:"flex",pack:"center",align:"center",onClose:function(){e.focus(),u.done()},onSubmit:function(e){var r,s,l,c;return e.preventDefault(),s=o.find("#case").checked(),c=o.find("#words").checked(),l=o.find("#find").value(),l.length?i.text==l&&i.caseState==s&&i.wholeWord==c?0===a(d+1).length?void n():(u.next(),void t()):(r=u.find(l,s,c),r||n(),o.statusbar.items().slice(1).disabled(0===r),t(),void(i={text:l,caseState:s,wholeWord:c})):(u.done(!1),void o.statusbar.items().slice(1).disabled(!0))},buttons:[{text:"Find",subtype:"primary",onclick:function(){o.submit()}},{text:"Replace",disabled:!0,onclick:function(){u.replace(o.find("#replace").value())||(o.statusbar.items().slice(1).disabled(!0),d=-1,i={})}},{text:"Replace all",disabled:!0,onclick:function(){u.replace(o.find("#replace").value(),!0,!0),o.statusbar.items().slice(1).disabled(!0),i={}}},{type:"spacer",flex:1},{text:"Prev",name:"prev",disabled:!0,onclick:function(){u.prev(),t()}},{text:"Next",name:"next",disabled:!0,onclick:function(){u.next(),t()}}],title:"Find and replace",items:{type:"form",padding:20,labelGap:30,spacing:10,items:[{type:"textbox",name:"find",size:40,label:"Find",value:r},{type:"textbox",name:"replace",size:40,label:"Replace with"},{type:"checkbox",name:"case",text:"Match case",label:" "},{type:"checkbox",name:"words",text:"Whole words",label:" "}]}})}function r(e){var t=e.getAttribute("data-mce-index");return"number"==typeof t?""+t:t}function i(n){var r,i;return i=e.dom.create("span",{"data-mce-bogus":1}),i.className="mce-match-marker",r=e.getBody(),u.done(!1),t(n,r,i,!1,e.schema)}function o(e){var t=e.parentNode;e.firstChild&&t.insertBefore(e.firstChild,e),e.parentNode.removeChild(e)}function a(t){var n,i=[];if(n=tinymce.toArray(e.getBody().getElementsByTagName("span")),n.length)for(var o=0;o<n.length;o++){var a=r(n[o]);null!==a&&a.length&&a===t.toString()&&i.push(n[o])}return i}function s(t){var n=d,r=e.dom;t=t!==!1,t?n++:n--,r.removeClass(a(d),"mce-match-marker-selected");var i=a(n);return i.length?(r.addClass(a(n),"mce-match-marker-selected"),e.selection.scrollIntoView(i[0]),n):-1}function l(t){var n=e.dom,r=t.parentNode;n.remove(t),n.isEmpty(r)&&n.remove(r)}function c(e){var t=r(e);return null!==t&&t.length>0}var u=this,d=-1;u.init=function(e){e.addMenuItem("searchreplace",{text:"Find and replace",shortcut:"Meta+F",onclick:n,separator:"before",context:"edit"}),e.addButton("searchreplace",{tooltip:"Find and replace",shortcut:"Meta+F",onclick:n}),e.addCommand("SearchReplace",n),e.shortcuts.add("Meta+F","",n)},u.find=function(e,t,n){e=e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&"),e=n?"\\b"+e+"\\b":e;var r=i(new RegExp(e,t?"g":"gi"));return r&&(d=-1,d=s(!0)),r},u.next=function(){var e=s(!0);e!==-1&&(d=e)},u.prev=function(){var e=s(!1);e!==-1&&(d=e)},u.replace=function(t,n,i){var s,f,p,m,h,g,v=d;for(n=n!==!1,p=e.getBody(),f=tinymce.grep(tinymce.toArray(p.getElementsByTagName("span")),c),s=0;s<f.length;s++){var b=r(f[s]);if(m=h=parseInt(b,10),i||m===d){for(t.length?(f[s].firstChild.nodeValue=t,o(f[s])):l(f[s]);f[++s];){if(m=parseInt(r(f[s]),10),m!==h){s--;break}l(f[s])}n&&v--}else h>d&&f[s].setAttribute("data-mce-index",h-1)}return e.undoManager.add(),d=v,n?(g=a(v+1).length>0,u.next()):(g=a(v-1).length>0,u.prev()),!i&&g},u.done=function(t){var n,i,a,s;for(i=tinymce.toArray(e.getBody().getElementsByTagName("span")),n=0;n<i.length;n++){var l=r(i[n]);null!==l&&l.length&&(l===d.toString()&&(a||(a=i[n].firstChild),s=i[n].firstChild),o(i[n]))}if(a&&s){var c=e.dom.createRng();return c.setStart(a,0),c.setEnd(s,s.data.length),t!==!1&&e.selection.setRng(c),c}}}tinymce.PluginManager.add("searchreplace",n)}();